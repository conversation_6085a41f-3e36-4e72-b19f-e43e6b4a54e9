/**
 * Static progress step definitions for ConvertIQ analysis
 * Used by both AnalysisProgressTracker.vue and FullScreenProgressTracker.vue
 */

export interface ProgressStepDefinition {
  id: number;
  name: string;
  description: string;
  icon?: string;
  estimatedDuration?: number; // in seconds
}

export const PROGRESS_STEPS: ProgressStepDefinition[] = [
  {
    id: 1,
    name: "Initializing Analysis",
    description: "Setting up analysis environment and preparing data collection",
    icon: "Settings",
    estimatedDuration: 5
  },
  {
    id: 2,
    name: "Generating Page Summary",
    description: "AI is analyzing your website's overall structure and content strategy",
    icon: "FileText",
    estimatedDuration: 15
  },
  {
    id: 3,
    name: "Analyzing Pros & Cons",
    description: "Identifying website strengths and weaknesses for conversion optimization",
    icon: "Scale",
    estimatedDuration: 20
  },
  {
    id: 4,
    name: "Lead Insights Analysis",
    description: "Evaluating lead generation potential and qualification strategies",
    icon: "Users",
    estimatedDuration: 18
  },
  {
    id: 5,
    name: "Performance Analysis",
    description: "Running Lighthouse performance tests and Core Web Vitals assessment",
    icon: "Zap",
    estimatedDuration: 25
  },
  {
    id: 6,
    name: "SEO & Technical Analysis",
    description: "Analyzing technical SEO factors and search engine optimization opportunities",
    icon: "Search",
    estimatedDuration: 20
  },
  {
    id: 7,
    name: "AI Performance Impact",
    description: "Generating AI-powered performance insights and optimization recommendations",
    icon: "TrendingUp",
    estimatedDuration: 22
  },
  {
    id: 8,
    name: "Chat Context Generation",
    description: "Preparing contextual AI chat interface with website-specific insights",
    icon: "MessageSquare",
    estimatedDuration: 15
  },
  {
    id: 9,
    name: "Finalizing Analysis",
    description: "Compiling results and preparing comprehensive conversion analysis report",
    icon: "CheckCircle",
    estimatedDuration: 10
  }
];

export const TOTAL_STEPS = PROGRESS_STEPS.length;

/**
 * Get progress step by ID
 */
export function getProgressStep(stepId: number): ProgressStepDefinition | undefined {
  return PROGRESS_STEPS.find(step => step.id === stepId);
}

/**
 * Get progress percentage based on current step
 */
export function getProgressPercentage(currentStep: number, isCompleted: boolean = false): number {
  if (isCompleted) return 100;
  if (currentStep <= 0) return 0;
  if (currentStep > TOTAL_STEPS) return 100;
  
  // Calculate percentage: step N means steps 1 to N-1 are complete, step N is in progress
  const completedSteps = Math.max(0, currentStep - 1);
  const progressInCurrentStep = 0.5; // Assume 50% progress in current step
  
  return Math.min(100, Math.round(((completedSteps + progressInCurrentStep) / TOTAL_STEPS) * 100));
}

/**
 * Get estimated time remaining based on current step
 */
export function getEstimatedTimeRemaining(currentStep: number): number {
  if (currentStep <= 0 || currentStep > TOTAL_STEPS) return 0;
  
  let remainingTime = 0;
  for (let i = currentStep; i <= TOTAL_STEPS; i++) {
    const step = getProgressStep(i);
    if (step?.estimatedDuration) {
      remainingTime += step.estimatedDuration;
    }
  }
  
  return remainingTime;
}

/**
 * Format time in seconds to human readable format
 */
export function formatTimeRemaining(seconds: number): string {
  if (seconds <= 0) return "Almost done";
  if (seconds < 60) return `${seconds}s remaining`;
  
  const minutes = Math.ceil(seconds / 60);
  if (minutes === 1) return "About 1 minute remaining";
  return `About ${minutes} minutes remaining`;
}

/**
 * Get step status based on current progress
 */
export function getStepStatus(stepId: number, currentStep: number, isCompleted: boolean): 'pending' | 'in_progress' | 'completed' {
  if (isCompleted) return 'completed';
  if (stepId < currentStep) return 'completed';
  if (stepId === currentStep) return 'in_progress';
  return 'pending';
}
