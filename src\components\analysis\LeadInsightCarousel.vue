<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import type { Database } from '../../types/supabase';
import { 
  Info,
  AlertCircle,
  TrendingUp,
  ChevronLeft,
  ChevronRight,
  Lightbulb,
  Target
} from 'lucide-vue-next';

type LeadQuestion = {
    questionType: string;
    questionText: string;
    contextExplanation: string;
    qualificationValue: string;
    suggestedResponse: string;
    priorityLevel: number;
};

const props = defineProps<{
  questions: LeadQuestion[];
}>();

const container = ref<HTMLElement | null>(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
const isDragging = ref(false);
const startX = ref(0);
const scrollLeft = ref(0);

onMounted(() => {
  nextTick(() => {
    handleScroll();
    window.addEventListener('resize', handleScroll);
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleScroll);
});

const handleScroll = () => {
  if (!container.value) return;
  const { scrollLeft, scrollWidth, clientWidth } = container.value;
  const tolerance = 1;
  showLeftArrow.value = scrollLeft > tolerance;
  showRightArrow.value = scrollLeft < scrollWidth - clientWidth - tolerance;
};

const scroll = (direction: 'left' | 'right') => {
  if (!container.value) return;
  const cards = Array.from(container.value.children) as HTMLElement[];
  const cardWidth = cards[0]?.offsetWidth || 0;
  const currentScroll = container.value.scrollLeft;
  const containerCenter = currentScroll + container.value.clientWidth / 2;

  let targetCard;

  if (direction === 'right') {
    targetCard = cards.find(card => card.offsetLeft + card.offsetWidth / 2 > containerCenter + cardWidth / 2);
    if (!targetCard) targetCard = cards[cards.length - 1];
  } else {
    targetCard = cards.slice().reverse().find(card => card.offsetLeft + card.offsetWidth / 2 < containerCenter - cardWidth / 2);
    if (!targetCard) targetCard = cards[0];
  }

  if (targetCard) {
    container.value.scrollTo({
      left: targetCard.offsetLeft - (container.value.clientWidth - targetCard.offsetWidth) / 2,
      behavior: 'smooth'
    });
  }
};

const startDragging = (e: MouseEvent) => {
  if (!container.value) return;
  isDragging.value = true;
  startX.value = e.pageX - container.value.offsetLeft;
  scrollLeft.value = container.value.scrollLeft;
  container.value.style.cursor = 'grabbing';
  container.value.style.scrollSnapType = 'none';
};

const stopDragging = () => {
  if (!isDragging.value) return;
  isDragging.value = false;
  if (container.value) {
    container.value.style.cursor = 'grab';
    container.value.style.scrollSnapType = 'x mandatory';
    const cards = Array.from(container.value.children) as HTMLElement[];
    const containerCenter = container.value.scrollLeft + container.value.clientWidth / 2;
    
    const closestCard = cards.reduce((prev, curr) => {
      const prevCenter = prev.offsetLeft + prev.offsetWidth / 2;
      const currCenter = curr.offsetLeft + curr.offsetWidth / 2;
      return (Math.abs(currCenter - containerCenter) < Math.abs(prevCenter - containerCenter) ? curr : prev);
    });

    container.value.scrollTo({
      left: closestCard.offsetLeft - (container.value.clientWidth - closestCard.offsetWidth) / 2,
      behavior: 'smooth'
    });
  }
};

const onDrag = (e: MouseEvent) => {
  if (!isDragging.value || !container.value) return;
  e.preventDefault();
  const x = e.pageX - container.value.offsetLeft;
  const walk = (x - startX.value) * 2;
  container.value.scrollLeft = scrollLeft.value - walk;
};

const getQuestionTypeColor = (type: string) => {
  const colors = {
    qualification: 'bg-blue-100 text-blue-800',
    concern: 'bg-orange-100 text-orange-800',
    business: 'bg-green-100 text-green-800',
    objection: 'bg-red-100 text-red-800'
  };
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
};

const getPriorityDetails = (level: number) => {
    switch (level) {
        case 5: return { text: 'Highest', color: 'bg-red-100 text-red-800' };
        case 4: return { text: 'High', color: 'bg-orange-100 text-orange-800' };
        case 3: return { text: 'Medium', color: 'bg-yellow-100 text-yellow-800' };
        case 2: return { text: 'Low', color: 'bg-blue-100 text-blue-800' };
        case 1: return { text: 'Lowest', color: 'bg-green-100 text-green-800' };
        default: return { text: 'Medium', color: 'bg-gray-100 text-gray-800' };
    }
};

</script>

<template>
  <div class="relative group">
    <button
        @click="scroll('left')"
        v-show="showLeftArrow"
        class="absolute top-1/2 -translate-y-1/2 left-0 z-10 bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 transition-opacity opacity-0 group-hover:opacity-100 disabled:opacity-0"
    >
        <ChevronLeft class="w-5 h-5" />
    </button>

    <div
        ref="container"
        @scroll="handleScroll"
        @mousedown.prevent="startDragging"
        @mouseleave="stopDragging"
        @mouseup="stopDragging"
        @mousemove="onDrag"
        class="flex overflow-x-auto scroll-smooth snap-x snap-mandatory scrollbar-hide py-4 fade-edges"
        style="cursor: grab; padding-left: 5%; padding-right: 5%;"
    >
        <div 
            v-for="(question, index) in questions" 
            :key="index" 
            class="snap-center flex-shrink-0 w-11/12 md:w-5/6 lg:w-3/4 border border-gray-200 rounded-lg p-6 flex flex-col mx-2"
        >
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <div class="flex items-center mb-3">
                        <span
                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mr-3"
                            :class="getQuestionTypeColor(question.questionType)"
                        >
                            {{ question.questionType }}
                        </span>
                        <span
                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                            :class="getPriorityDetails(question.priorityLevel).color"
                        >
                            {{ getPriorityDetails(question.priorityLevel).text }} Priority
                        </span>
                    </div>
                    <h5 class="font-semibold text-gray-900 mb-3 text-lg leading-tight">{{ question.questionText }}</h5>
                </div>
            </div>

            <div class="space-y-4 text-sm">
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <p class="font-semibold text-blue-900 mb-2 flex items-center">
                        <Info class="w-4 h-4 mr-2" />
                        Why this question arises:
                    </p>
                    <p class="text-blue-800 leading-relaxed">{{ question.contextExplanation }}</p>
                </div>

                <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                    <p class="font-semibold text-green-900 mb-2 flex items-center">
                        <Target class="w-4 h-4 mr-2" />
                        Qualification value:
                    </p>
                    <p class="text-green-800 leading-relaxed">{{ question.qualificationValue }}</p>
                </div>

                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <p class="font-semibold text-gray-900 mb-2 flex items-center">
                        <Lightbulb class="w-4 h-4 mr-2" />
                        Suggested response:
                    </p>
                    <p class="text-gray-800 leading-relaxed">{{ question.suggestedResponse }}</p>
                </div>
            </div>
        </div>
    </div>

    <button
        @click="scroll('right')"
        v-show="showRightArrow"
        class="absolute top-1/2 -translate-y-1/2 right-0 z-10 bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 transition-opacity opacity-0 group-hover:opacity-100 disabled:opacity-0"
    >
        <ChevronRight class="w-5 h-5" />
    </button>
  </div>
</template>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.fade-edges {
  --fade-width: 3%;
  -webkit-mask-image: linear-gradient(to right, transparent, black var(--fade-width), black calc(100% - var(--fade-width)), transparent);
  mask-image: linear-gradient(to right, transparent, black var(--fade-width), black calc(100% - var(--fade-width)), transparent);
}
</style>
