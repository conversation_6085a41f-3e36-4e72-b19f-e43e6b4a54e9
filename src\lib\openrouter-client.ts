/**
 * Enhanced OpenRouter API Client with robust error handling and retry logic
 */

import { loggedFetch, log } from './logger';

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

if (!OPENROUTER_API_KEY) {
  throw new Error('Missing PUBLIC_OPENROUTER_API_KEY environment variable');
}

// Model configuration with fallbacks
const models = {
  'qwen': 'qwen/qwen-2.5-72b-instruct:free',
  'claude-sonnet': 'anthropic/claude-3.5-sonnet',
  'gpt-4o': 'openai/gpt-4o',
  'gpt-4o-mini': 'openai/gpt-4o-mini'
};

const activeModel = models['qwen'];
const fallbackModel = models['gpt-4o-mini'];

export interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenRouterRequest {
  model: string;
  messages: OpenRouterMessage[];
  max_tokens?: number;
  temperature?: number;
  response_format?: { type: 'json_object' };
}

export interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  error?: {
    message: string;
    type: string;
    code?: string;
  };
}

/**
 * Test OpenRouter API connectivity and authentication
 */
export async function testOpenRouterConnection(): Promise<boolean> {
  try {
    log.info('Testing OpenRouter API connection...');
    
    const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: activeModel,
        messages: [
          { role: 'user', content: 'Hello, this is a connection test.' }
        ],
        max_tokens: 10
      })
    });

    if (response.ok) {
      log.info('OpenRouter API connection successful');
      return true;
    } else {
      const errorData = await response.json();
      log.error(`OpenRouter API connection failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    log.error(`OpenRouter API connection test failed: ${error instanceof Error ? error.message : error}`);
    return false;
  }
}

/**
 * Make a robust OpenRouter API call with retry logic and fallback models
 */
export async function callOpenRouter(
  request: OpenRouterRequest,
  useRetry: boolean = true,
  useFallback: boolean = true
): Promise<OpenRouterResponse> {
  const maxRetries = useRetry ? 3 : 1;
  const models = [request.model];
  
  if (useFallback && request.model !== fallbackModel) {
    models.push(fallbackModel);
  }

  for (const model of models) {
    const requestWithModel = { ...request, model };
    
    try {
      log.debug(`Attempting OpenRouter call with model: ${model}`);
      
      const response = await loggedFetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': typeof window !== 'undefined' ? window.location.origin : 'https://convertiq.app',
          'X-Title': 'ConvertIQ Analysis Platform'
        },
        body: JSON.stringify(requestWithModel)
      });

      const data: OpenRouterResponse = await response.json();

      if (data.error) {
        log.error(`OpenRouter API Error with model ${model}: ${data.error.message}`);
        
        // If it's a model-specific error and we have fallback, try next model
        if (useFallback && models.length > 1 && model !== models[models.length - 1]) {
          log.warn(`Trying fallback model due to error: ${data.error.message}`);
          continue;
        }
        
        throw new Error(data.error.message || 'OpenRouter API error');
      }

      if (!data.choices || data.choices.length === 0) {
        log.error(`Invalid response from OpenRouter with model ${model}: No choices returned`);
        
        // Try fallback model if available
        if (useFallback && models.length > 1 && model !== models[models.length - 1]) {
          log.warn('Trying fallback model due to invalid response');
          continue;
        }
        
        throw new Error('Invalid response from OpenRouter API');
      }

      log.info(`OpenRouter API call successful with model: ${model}`);
      return data;

    } catch (error) {
      log.error(`OpenRouter API call failed with model ${model}: ${error instanceof Error ? error.message : error}`);
      
      // If this is the last model, throw the error
      if (model === models[models.length - 1]) {
        throw error;
      }
      
      // Otherwise, try the next model
      log.warn(`Trying fallback model due to error: ${error instanceof Error ? error.message : error}`);
    }
  }

  throw new Error('All OpenRouter models failed');
}

/**
 * Simplified wrapper for common OpenRouter calls
 */
export async function generateAIResponse(
  systemPrompt: string,
  userPrompt: string,
  options: {
    maxTokens?: number;
    temperature?: number;
    jsonMode?: boolean;
    useRetry?: boolean;
    useFallback?: boolean;
  } = {}
): Promise<string> {
  const {
    maxTokens = 1500,
    temperature = 0.7,
    jsonMode = false,
    useRetry = true,
    useFallback = true
  } = options;

  const request: OpenRouterRequest = {
    model: activeModel,
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ],
    max_tokens: maxTokens,
    temperature
  };

  if (jsonMode) {
    request.response_format = { type: 'json_object' };
  }

  try {
    const response = await callOpenRouter(request, useRetry, useFallback);
    return response.choices[0].message.content;
  } catch (error) {
    log.error(`Failed to generate AI response: ${error instanceof Error ? error.message : error}`);
    throw error;
  }
}

/**
 * Health check for OpenRouter service
 */
export async function checkOpenRouterHealth(): Promise<{
  isHealthy: boolean;
  latency?: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    const isConnected = await testOpenRouterConnection();
    const latency = Date.now() - startTime;
    
    return {
      isHealthy: isConnected,
      latency: isConnected ? latency : undefined,
      error: isConnected ? undefined : 'Connection test failed'
    };
  } catch (error) {
    return {
      isHealthy: false,
      latency: undefined,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
