// Complete system test for ConvertIQ
const testAnalysisId = '2845b647-3c7e-4623-b058-1fbd7174e0fd';

console.log('🚀 Starting ConvertIQ Complete System Test');
console.log('Analysis ID:', testAnalysisId);

// Test 1: Check if analysis exists and has pending status
async function testAnalysisExists() {
  console.log('\n📋 Test 1: Checking analysis exists...');
  
  try {
    const response = await fetch(`http://localhost:4321/api/analysis-progress?analysisId=${testAnalysisId}`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Analysis found');
      console.log('Status:', data.status);
      console.log('Progress:', data.progressPercentage + '%');
      console.log('Steps:', data.steps?.length || 0);
      return true;
    } else {
      console.log('❌ Analysis not found:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error checking analysis:', error.message);
    return false;
  }
}

// Test 2: Test comprehensive analysis API
async function testComprehensiveAnalysis() {
  console.log('\n🔍 Test 2: Testing comprehensive analysis API...');
  
  try {
    const response = await fetch('http://localhost:4321/api/comprehensive-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://example.com/',
        analysisId: testAnalysisId
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Comprehensive analysis API working');
      console.log('Suggestions count:', data.suggestionsCount);
      return true;
    } else {
      console.log('❌ Comprehensive analysis failed:', data.error || data.details);
      return false;
    }
  } catch (error) {
    console.log('❌ Error in comprehensive analysis:', error.message);
    return false;
  }
}

// Test 3: Test AI insights generation
async function testAIInsightsGeneration() {
  console.log('\n🤖 Test 3: Testing AI insights generation...');
  
  try {
    const response = await fetch('http://localhost:4321/api/generate-ai-insights-sequential', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        analysisId: testAnalysisId
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ AI insights generation working');
      return true;
    } else {
      console.log('❌ AI insights generation failed:', data.error || data.details);
      return false;
    }
  } catch (error) {
    console.log('❌ Error in AI insights generation:', error.message);
    return false;
  }
}

// Test 4: Check final analysis data
async function testFinalAnalysisData() {
  console.log('\n📊 Test 4: Checking final analysis data...');
  
  try {
    const response = await fetch(`http://localhost:4321/api/analysis-progress?analysisId=${testAnalysisId}`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Final analysis data retrieved');
      console.log('Status:', data.status);
      console.log('All content generated:', data.contentStatus?.allContent);
      console.log('Pros & Cons generated:', data.contentStatus?.prosAndCons);
      console.log('Lead insights generated:', data.contentStatus?.leadInsights);
      console.log('Performance insights generated:', data.contentStatus?.performanceInsights);
      console.log('SEO insights generated:', data.contentStatus?.seoInsights);
      console.log('Chat context generated:', data.contentStatus?.chatContext);
      return data.isComplete;
    } else {
      console.log('❌ Failed to get final analysis data:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error checking final analysis data:', error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🧪 Running ConvertIQ Complete System Tests...\n');
  
  const test1 = await testAnalysisExists();
  if (!test1) return;
  
  const test2 = await testComprehensiveAnalysis();
  const test3 = await testAIInsightsGeneration();
  
  // Wait a bit for processing
  console.log('\n⏳ Waiting for processing to complete...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  const test4 = await testFinalAnalysisData();
  
  console.log('\n📋 Test Results Summary:');
  console.log('Analysis exists:', test1 ? '✅' : '❌');
  console.log('Comprehensive analysis:', test2 ? '✅' : '❌');
  console.log('AI insights generation:', test3 ? '✅' : '❌');
  console.log('Final data complete:', test4 ? '✅' : '❌');
  
  const allPassed = test1 && test2 && test3 && test4;
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  if (allPassed) {
    console.log('\n🎉 ConvertIQ system is working perfectly!');
    console.log(`🔗 Test the UI at: http://localhost:4321/dashboard/analysis/${testAnalysisId}`);
  }
}

// Run the tests
runAllTests().catch(console.error);
