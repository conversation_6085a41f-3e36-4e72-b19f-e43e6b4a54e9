<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';
import { CheckCircle, XCircle, TrendingUp, AlertTriangle, Lightbulb, Loader2 } from 'lucide-vue-next';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const aiInsights = ref<{
  strengths: Array<{ 
    title: string; 
    description: string; 
    evidence: string; 
    impactExplanation: string; 
    businessValue: string;
    priorityScore: number;
    category: string;
  }>;
  weaknesses: Array<{ 
    title: string; 
    description: string; 
    evidence: string; 
    impactExplanation: string; 
    implementationSteps: string;
    businessValue: string;
    priorityScore: number;
    category: string;
  }>;
  overallAssessment: string;
} | null>(null);

const isLoadingAI = ref(false);
const aiError = ref<string | null>(null);

onMounted(async () => {
  await loadAIInsights();

  // Listen for refresh events from parent component
  document.addEventListener('refresh-tab-data', (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail?.analysisId === props.analysis.id) {
      console.log('Refreshing pros/cons data...');
      loadAIInsights();
    }
  });
});

async function loadAIInsights() {
  if (!props.analysis) return;

  isLoadingAI.value = true;
  aiError.value = null;

  try {
    // Check if AI insights already exist in database with enhanced data
    const { data: existingInsights } = await supabase
      .from('ai_insights')
      .select(`
        *,
        suggestion_categories(name)
      `)
      .eq('analysis_id', props.analysis.id)
      .order('display_order', { ascending: true });

    if (existingInsights && existingInsights.length > 0) {
      // Use existing insights with enhanced data
      const strengths = existingInsights
        .filter(insight => insight.insight_type === 'strength')
        .map(insight => ({
          title: insight.title,
          description: insight.description,
          evidence: insight.evidence || '',
          impactExplanation: insight.impact_explanation || '',
          implementationSteps: insight.implementation_steps || '',
          businessValue: insight.business_value || '',
          priorityScore: insight.priority_score || 0,
          impactScore: insight.impact_score || 0,
          effortRequired: insight.effort_required || 'Medium',
          timelineEstimate: insight.timeline_estimate || 'To be determined',
          category: insight.suggestion_categories?.name || 'conversion'
        }));

      const weaknesses = existingInsights
        .filter(insight => insight.insight_type === 'weakness')
        .map(insight => ({
          title: insight.title,
          description: insight.description,
          evidence: insight.evidence || '',
          impactExplanation: insight.impact_explanation || '',
          implementationSteps: insight.implementation_steps || '',
          businessValue: insight.business_value || '',
          priorityScore: insight.priority_score || 0,
          impactScore: insight.impact_score || 0,
          effortRequired: insight.effort_required || 'Medium',
          timelineEstimate: insight.timeline_estimate || 'To be determined',
          category: insight.suggestion_categories?.name || 'conversion'
        }));

      aiInsights.value = {
        strengths,
        weaknesses,
        overallAssessment: 'AI analysis based on actual website content and structure'
      };
    } else {
      // No insights found - content may still be generating
      aiError.value = 'Pros & cons analysis is being generated. Please wait for the analysis to complete.';
      aiInsights.value = null;
    }
  } catch (error) {
    console.error('Error loading AI insights:', error);
    aiError.value = 'Failed to load AI insights. Please refresh the page or try again later.';
    aiInsights.value = null;
  } finally {
    isLoadingAI.value = false;
  }
}

const overallScore = computed(() => {
  const scores = [
    props.analysis.score || 0,
    (props.analysis.performance_score || 0) / 10,
    (props.analysis.seo_score || 0) / 10
  ].filter(score => score > 0);
  
  if (scores.length === 0) return 0;
  return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length * 10) / 10;
});

const getOverallGrade = computed(() => {
  const score = overallScore.value;
  if (score >= 8) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-50' };
  if (score >= 6) return { grade: 'B', color: 'text-blue-600', bg: 'bg-blue-50' };
  if (score >= 4) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-50' };
  if (score >= 2) return { grade: 'D', color: 'text-orange-600', bg: 'bg-orange-50' };
  return { grade: 'F', color: 'text-red-600', bg: 'bg-red-50' };
});

const getCategoryColor = (category: string) => {
  const colors = {
    conversion: 'bg-blue-100 text-blue-800',
    performance: 'bg-green-100 text-green-800',
    seo: 'bg-purple-100 text-purple-800',
    content: 'bg-orange-100 text-orange-800',
    trust: 'bg-indigo-100 text-indigo-800',
    ux: 'bg-pink-100 text-pink-800'
  };
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
};
</script>

<template>
  <div class="space-y-8">
    <!-- AI-Generated Analysis Overview -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 class="text-lg font-semibold text-gray-900">AI-Powered Pros & Cons Analysis</h3>
          
          <!-- AI Status Indicators -->
          <div v-if="isLoadingAI" class="ml-3 flex items-center">
            <Loader2 class="w-4 h-4 animate-spin text-blue-600 mr-1" />
            <span class="text-xs text-blue-600">Generating AI insights...</span>
          </div>
          <div v-else-if="aiError" class="ml-3 flex items-center">
            <AlertTriangle class="w-4 h-4 text-yellow-500 mr-1" />
            <span class="text-xs text-yellow-600">Using fallback analysis</span>
          </div>
          <div v-else-if="aiInsights" class="ml-3 flex items-center">
            <CheckCircle class="w-4 h-4 text-green-500 mr-1" />
            <span class="text-xs text-green-600">AI-powered insights</span>
          </div>
        </div>
        
        <!-- Overall Grade -->
        <div class="flex items-center space-x-3">
          <div class="text-right">
            <p class="text-sm text-gray-500">Overall Grade</p>
          </div>
          <div
            class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold"
            :class="[getOverallGrade.color, getOverallGrade.bg]"
          >
            {{ getOverallGrade.grade }}
          </div>
        </div>
      </div>

      <!-- AI Overall Assessment -->
      <div v-if="aiInsights?.overallAssessment" class="bg-blue-50 rounded-lg p-4 border border-blue-200 mb-6">
        <p class="text-blue-800 leading-relaxed">{{ aiInsights.overallAssessment }}</p>
      </div>

      <!-- AI Insights Content -->
      <div v-if="aiInsights" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- AI-Generated Strengths -->
        <div class="space-y-4">
          <div class="flex items-center">
            <CheckCircle class="w-5 h-5 text-green-500 mr-2" />
            <h4 class="font-medium text-green-900">AI-Identified Strengths</h4>
          </div>
          
          <div class="space-y-3">
            <div 
              v-for="(strength, index) in aiInsights.strengths" 
              :key="`strength-${index}`"
              class="p-4 bg-green-50 rounded-lg border border-green-200"
            >
              <div class="flex items-start justify-between mb-2">
                <h5 class="font-medium text-green-900 text-sm">{{ strength.title }}</h5>
                <span 
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="getCategoryColor(strength.category)"
                >
                  {{ strength.category }}
                </span>
              </div>
              <p class="text-sm text-green-800 mb-2">{{ strength.description }}</p>
              <div class="text-xs text-green-700 space-y-1">
                <p><strong>Evidence:</strong> {{ strength.evidence }}</p>
                <p><strong>Impact:</strong> {{ strength.impactExplanation }}</p>
                <p><strong>Business Value:</strong> {{ strength.businessValue }}</p>
              </div>
            </div>
            
            <div v-if="aiInsights.strengths.length === 0" class="text-center py-8 text-gray-500">
              <CheckCircle class="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p class="text-sm">No specific strengths identified in AI analysis</p>
            </div>
          </div>
        </div>

        <!-- AI-Generated Weaknesses -->
        <div class="space-y-4">
          <div class="flex items-center">
            <XCircle class="w-5 h-5 text-red-500 mr-2" />
            <h4 class="font-medium text-red-900">AI-Identified Improvement Areas</h4>
          </div>
          
          <div class="space-y-3">
            <div 
              v-for="(weakness, index) in aiInsights.weaknesses" 
              :key="`weakness-${index}`"
              class="p-4 bg-red-50 rounded-lg border border-red-200"
            >
              <div class="flex items-start justify-between mb-2">
                <h5 class="font-medium text-red-900 text-sm">{{ weakness.title }}</h5>
                <span 
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="getCategoryColor(weakness.category)"
                >
                  {{ weakness.category }}
                </span>
              </div>
              <p class="text-sm text-red-800 mb-2">{{ weakness.description }}</p>
              <div class="text-xs text-red-700 space-y-1">
                <p><strong>Evidence:</strong> {{ weakness.evidence }}</p>
                <p><strong>Impact:</strong> {{ weakness.impactExplanation }}</p>
                <p><strong>Business Value:</strong> {{ weakness.businessValue }}</p>
              </div>
              
              <!-- Implementation Steps -->
              <div v-if="weakness.implementationSteps" class="mt-3 p-2 bg-red-100 rounded border border-red-300">
                <div class="flex items-start">
                  <Lightbulb class="w-3 h-3 text-red-600 mr-1 mt-0.5 flex-shrink-0" />
                  <div>
                    <p class="text-xs font-medium text-red-900 mb-1">How to Fix:</p>
                    <p class="text-xs text-red-800">{{ weakness.implementationSteps }}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-if="aiInsights.weaknesses.length === 0" class="text-center py-8 text-gray-500">
              <CheckCircle class="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p class="text-sm">No specific issues identified in AI analysis</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="aiError" class="text-center py-12">
        <AlertTriangle class="w-8 h-8 text-yellow-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">AI Analysis Unavailable</h3>
        <p class="text-gray-600 mb-4">{{ aiError }}</p>
        <button 
          @click="loadAIInsights" 
          class="btn-primary"
        >
          Retry AI Analysis
        </button>
      </div>
    </div>

    <!-- Business Impact Analysis -->
    <div v-if="aiInsights" class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center mb-4">
        <TrendingUp class="w-5 h-5 text-gray-400 mr-2" />
        <h3 class="text-lg font-semibold text-gray-900">Business Impact Forecast</h3>
      </div>

      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ aiInsights.strengths.length }}</div>
            <div class="text-sm text-blue-800">Strengths to Leverage</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-red-600">{{ aiInsights.weaknesses.length }}</div>
            <div class="text-sm text-red-800">Issues to Address</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">
              {{ Math.round((aiInsights.strengths.reduce((sum, s) => sum + s.priorityScore, 0) / Math.max(aiInsights.strengths.length, 1))) }}%
            </div>
            <div class="text-sm text-green-800">Optimization Potential</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}
</style>