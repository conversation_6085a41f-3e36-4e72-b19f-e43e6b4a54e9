<template>
  <div class="advanced-metrics-panel">
    <!-- Network Performance Metrics -->
    <div class="metrics-section">
      <h3 class="section-title">
        <Globe class="w-5 h-5 text-blue-600" />
        Network Performance
      </h3>
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <Clock class="w-4 h-4 text-orange-500" />
            <span class="metric-label">TTFB</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Time to First Byte - Server response time</div>
            </div>
          </div>
          <div class="metric-value" :class="getTTFBClass(networkMetrics?.ttfb)">
            {{ formatTime(networkMetrics?.ttfb) }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Activity class="w-4 h-4 text-green-500" />
            <span class="metric-label">Total Requests</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Number of network requests made</div>
            </div>
          </div>
          <div class="metric-value">
            {{ networkMetrics?.totalRequests || 0 }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Download class="w-4 h-4 text-purple-500" />
            <span class="metric-label">Transfer Size</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Total bytes transferred over network</div>
            </div>
          </div>
          <div class="metric-value">
            {{ formatBytes(networkMetrics?.totalTransferSize) }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Layers class="w-4 h-4 text-indigo-500" />
            <span class="metric-label">DOM Elements</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Number of DOM elements on the page</div>
            </div>
          </div>
          <div class="metric-value" :class="getDOMSizeClass(networkMetrics?.domContentLoaded)">
            {{ networkMetrics?.domContentLoaded || 0 }}
          </div>
        </div>
      </div>
    </div>

    <!-- Core Web Vitals Details -->
    <div class="metrics-section">
      <h3 class="section-title">
        <Zap class="w-5 h-5 text-yellow-600" />
        Core Web Vitals Details
      </h3>
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <Eye class="w-4 h-4 text-blue-500" />
            <span class="metric-label">First Contentful Paint</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Time when first content appears</div>
            </div>
          </div>
          <div class="metric-value" :class="getFCPClass(timingMetrics?.firstContentfulPaint)">
            {{ formatTime(timingMetrics?.firstContentfulPaint) }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Target class="w-4 h-4 text-green-500" />
            <span class="metric-label">Speed Index</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">How quickly content is visually displayed</div>
            </div>
          </div>
          <div class="metric-value" :class="getSpeedIndexClass(timingMetrics?.speedIndex)">
            {{ formatTime(timingMetrics?.speedIndex) }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <MousePointer class="w-4 h-4 text-purple-500" />
            <span class="metric-label">Time to Interactive</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">When page becomes fully interactive</div>
            </div>
          </div>
          <div class="metric-value" :class="getTTIClass(timingMetrics?.timeToInteractive)">
            {{ formatTime(timingMetrics?.timeToInteractive) }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Pause class="w-4 h-4 text-red-500" />
            <span class="metric-label">Total Blocking Time</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Time main thread was blocked</div>
            </div>
          </div>
          <div class="metric-value" :class="getTBTClass(timingMetrics?.totalBlockingTime)">
            {{ formatTime(timingMetrics?.totalBlockingTime, true) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Resource Optimization -->
    <div class="metrics-section">
      <h3 class="section-title">
        <Settings class="w-5 h-5 text-gray-600" />
        Resource Optimization
      </h3>
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-header">
            <AlertTriangle class="w-4 h-4 text-orange-500" />
            <span class="metric-label">Render Blocking</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Resources blocking initial render</div>
            </div>
          </div>
          <div class="metric-value" :class="getOptimizationClass(advancedMetrics?.renderBlockingResources)">
            {{ advancedMetrics?.renderBlockingResources || 0 }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Scissors class="w-4 h-4 text-blue-500" />
            <span class="metric-label">Unused CSS</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">CSS rules not used on this page</div>
            </div>
          </div>
          <div class="metric-value" :class="getOptimizationClass(advancedMetrics?.unusedCssRules)">
            {{ advancedMetrics?.unusedCssRules || 0 }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Code class="w-4 h-4 text-yellow-500" />
            <span class="metric-label">Unused JavaScript</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">JavaScript not executed on this page</div>
            </div>
          </div>
          <div class="metric-value" :class="getOptimizationClass(advancedMetrics?.unusedJavaScript)">
            {{ advancedMetrics?.unusedJavaScript || 0 }}
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-header">
            <Image class="w-4 h-4 text-green-500" />
            <span class="metric-label">Image Optimization</span>
            <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
              <HelpCircle class="w-3 h-3 text-gray-400" />
              <div class="tooltip">Images that could use modern formats</div>
            </div>
          </div>
          <div class="metric-value" :class="getOptimizationClass(advancedMetrics?.modernImageFormats)">
            {{ advancedMetrics?.modernImageFormats || 0 }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  Globe, Clock, Activity, Download, Layers, Zap, Eye, Target, 
  MousePointer, Pause, Settings, AlertTriangle, Scissors, Code, 
  Image, HelpCircle 
} from 'lucide-vue-next';

interface Props {
  lighthouseData?: {
    timingMetrics?: {
      firstContentfulPaint?: number;
      speedIndex?: number;
      timeToInteractive?: number;
      totalBlockingTime?: number;
      firstMeaningfulPaint?: number;
      maxPotentialFid?: number;
    };
    networkMetrics?: {
      ttfb?: number;
      domContentLoaded?: number;
      totalRequests?: number;
      totalTransferSize?: number;
    };
    advancedMetrics?: {
      renderBlockingResources?: number;
      unusedCssRules?: number;
      unusedJavaScript?: number;
      modernImageFormats?: number;
      offscreenImages?: number;
    };
  };
}

const props = defineProps<Props>();

const timingMetrics = computed(() => props.lighthouseData?.timingMetrics || {});
const networkMetrics = computed(() => props.lighthouseData?.networkMetrics || {});
const advancedMetrics = computed(() => props.lighthouseData?.advancedMetrics || {});

// Utility functions
const formatTime = (time?: number, isMs = false) => {
  if (!time) return '0s';
  if (isMs) return `${Math.round(time)}ms`;
  return time < 1 ? `${Math.round(time * 1000)}ms` : `${time.toFixed(1)}s`;
};

const formatBytes = (bytes?: number) => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
};

// Performance class helpers
const getTTFBClass = (ttfb?: number) => {
  if (!ttfb) return 'text-gray-500';
  return ttfb > 600 ? 'text-red-600' : ttfb > 200 ? 'text-yellow-600' : 'text-green-600';
};

const getFCPClass = (fcp?: number) => {
  if (!fcp) return 'text-gray-500';
  return fcp > 1.8 ? 'text-red-600' : fcp > 1.0 ? 'text-yellow-600' : 'text-green-600';
};

const getSpeedIndexClass = (si?: number) => {
  if (!si) return 'text-gray-500';
  return si > 3.4 ? 'text-red-600' : si > 2.3 ? 'text-yellow-600' : 'text-green-600';
};

const getTTIClass = (tti?: number) => {
  if (!tti) return 'text-gray-500';
  return tti > 3.8 ? 'text-red-600' : tti > 2.5 ? 'text-yellow-600' : 'text-green-600';
};

const getTBTClass = (tbt?: number) => {
  if (!tbt) return 'text-gray-500';
  return tbt > 300 ? 'text-red-600' : tbt > 200 ? 'text-yellow-600' : 'text-green-600';
};

const getDOMSizeClass = (domSize?: number) => {
  if (!domSize) return 'text-gray-500';
  return domSize > 1500 ? 'text-red-600' : domSize > 800 ? 'text-yellow-600' : 'text-green-600';
};

const getOptimizationClass = (count?: number) => {
  if (!count) return 'text-green-600';
  return count > 10 ? 'text-red-600' : count > 5 ? 'text-yellow-600' : 'text-orange-600';
};

// Tooltip functions
const showTooltip = (event: MouseEvent) => {
  const tooltip = (event.target as HTMLElement).querySelector('.tooltip');
  if (tooltip) {
    (tooltip as HTMLElement).style.visibility = 'visible';
    (tooltip as HTMLElement).style.opacity = '1';
  }
};

const hideTooltip = (event: MouseEvent) => {
  const tooltip = (event.target as HTMLElement).querySelector('.tooltip');
  if (tooltip) {
    (tooltip as HTMLElement).style.visibility = 'hidden';
    (tooltip as HTMLElement).style.opacity = '0';
  }
};
</script>

<style scoped>
.advanced-metrics-panel {
  @apply space-y-6;
}

.metrics-section {
  @apply bg-white rounded-lg border border-gray-200 p-6;
}

.section-title {
  @apply flex items-center gap-2 text-lg font-semibold text-gray-900 mb-4;
}

.metrics-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.metric-card {
  @apply bg-gray-50 rounded-lg p-4 border border-gray-100 hover:border-gray-200 transition-colors duration-200;
}

.metric-header {
  @apply flex items-center gap-2 mb-2 relative;
}

.metric-label {
  @apply text-sm font-medium text-gray-600 flex-1;
}

.metric-value {
  @apply text-2xl font-bold;
}

.tooltip-trigger {
  @apply relative cursor-help;
}

.tooltip {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded whitespace-nowrap z-10 invisible opacity-0 transition-opacity duration-200;
}

.tooltip::after {
  @apply absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900;
  content: '';
}
</style>
