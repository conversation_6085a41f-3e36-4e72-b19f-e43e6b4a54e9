import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { log } from '../../lib/logger';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    log.info('Populating info content for tooltips and help text');

    // Define comprehensive info content for all metrics and features
    const infoContentData = [
      {
        content_key: 'performance-score',
        content_type: 'tooltip',
        title: 'Performance Score',
        content_text: 'Measures loading performance including First Contentful Paint, Largest Contentful Paint, and other speed metrics. Higher scores indicate faster loading times. Scored 0-100.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'accessibility-score',
        content_type: 'tooltip',
        title: 'Accessibility Score',
        content_text: 'Evaluates how accessible your website is to users with disabilities. Includes checks for alt text, color contrast, keyboard navigation, and screen reader compatibility. Scored 0-100.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'seo-score',
        content_type: 'tooltip',
        title: 'SEO Score',
        content_text: 'Measures search engine optimization factors including meta tags, heading structure, mobile-friendliness, and technical SEO elements. Higher scores improve search visibility. Scored 0-100.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'best-practices-score',
        content_type: 'tooltip',
        title: 'Best Practices Score',
        content_text: 'Evaluates adherence to web development best practices including HTTPS usage, console errors, deprecated APIs, and security measures. Scored 0-100.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'lcp',
        content_type: 'tooltip',
        title: 'Largest Contentful Paint (LCP)',
        content_text: 'Measures how long it takes for the main content to load. Good: ≤2.5s, Needs Improvement: ≤4s, Poor: >4s. Critical for user experience and SEO.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'fid',
        content_type: 'tooltip',
        title: 'First Input Delay (FID)',
        content_text: 'Measures how long it takes for the page to respond to user interactions. Good: ≤100ms, Needs Improvement: ≤300ms, Poor: >300ms. Affects user engagement.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'cls',
        content_type: 'tooltip',
        title: 'Cumulative Layout Shift (CLS)',
        content_text: 'Measures visual stability - how much content moves around while loading. Good: ≤0.1, Needs Improvement: ≤0.25, Poor: >0.25. Prevents user frustration.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'ttfb',
        content_type: 'tooltip',
        title: 'Time to First Byte (TTFB)',
        content_text: 'Measures server response time - how quickly your server responds to requests. Good: ≤200ms, Needs Improvement: ≤600ms, Poor: >600ms.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'conversion-score',
        content_type: 'tooltip',
        title: 'Conversion Score',
        content_text: 'AI-powered assessment of how well your website converts visitors into leads or customers. Based on design, messaging, trust signals, and user experience. Scored 0-10.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'lead-generation-score',
        content_type: 'tooltip',
        title: 'Lead Generation Score',
        content_text: 'Combines conversion optimization (50%), performance (30%), and SEO (20%) to predict lead capture potential. Higher scores indicate better lead generation capability. Scored 0-10.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'target-audience',
        content_type: 'tooltip',
        title: 'Target Audience Analysis',
        content_text: 'AI analysis of page content to identify the most likely prospects and their characteristics based on messaging, positioning, and value propositions.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'conversion-priority',
        content_type: 'tooltip',
        title: 'Conversion Priority',
        content_text: 'Shows which areas to focus on first for maximum lead generation impact. Based on current scores, improvement potential, and business impact.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'ai-insights',
        content_type: 'tooltip',
        title: 'AI-Generated Insights',
        content_text: 'Machine learning analysis of your website to identify strengths, weaknesses, and optimization opportunities based on conversion best practices.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'business-impact',
        content_type: 'tooltip',
        title: 'Business Impact Assessment',
        content_text: 'Estimated effect of implementing suggested improvements on your business metrics including lead generation, conversion rates, and revenue potential.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'implementation-effort',
        content_type: 'tooltip',
        title: 'Implementation Effort',
        content_text: 'Estimated time and resources required to implement suggested changes. Low: <1 day, Medium: 1-5 days, High: >5 days.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'priority-score',
        content_type: 'tooltip',
        title: 'Priority Score',
        content_text: 'AI-calculated priority based on impact potential, implementation effort, and business value. Higher scores indicate more important improvements.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'core-web-vitals',
        content_type: 'tooltip',
        title: 'Core Web Vitals',
        content_text: 'Google\'s key metrics for measuring user experience: LCP (loading), FID (interactivity), and CLS (visual stability). Critical for SEO rankings.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'mobile-performance',
        content_type: 'tooltip',
        title: 'Mobile Performance',
        content_text: 'How well your website performs on mobile devices. Critical since most users browse on mobile and Google uses mobile-first indexing.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'seo-technical',
        content_type: 'tooltip',
        title: 'Technical SEO',
        content_text: 'Backend optimization factors that affect search engine crawling and indexing including site structure, meta tags, and schema markup.',
        position_hint: 'top',
        trigger_event: 'hover'
      },
      {
        content_key: 'conversion-barriers',
        content_type: 'tooltip',
        title: 'Conversion Barriers',
        content_text: 'AI-identified obstacles that prevent visitors from converting, such as unclear messaging, poor trust signals, or confusing navigation.',
        position_hint: 'top',
        trigger_event: 'hover'
      }
    ];

    // Insert or update info content
    const { data, error } = await supabaseAdmin
      .from('info_content')
      .upsert(infoContentData, {
        onConflict: 'content_key'
      });

    if (error) {
      throw error;
    }

    log.info(`Successfully populated ${infoContentData.length} info content entries`);

    return new Response(JSON.stringify({
      success: true,
      message: `Populated ${infoContentData.length} info content entries`,
      count: infoContentData.length
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error populating info content: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to populate info content',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
