/**
 * ConvertIQ Critical Issues Test Script
 * 
 * This script tests all the critical fixes implemented for ConvertIQ:
 * 1. Live progress tracking
 * 2. Database population
 * 3. Analysis completion detection
 * 4. Real-time updates
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ntaqqukzcvhqhtvcqdfy.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test configuration
const TEST_ANALYSIS_ID = '19be674d-8d69-4f56-bb2e-e5fc5450f5d6';
const EMPTY_TABLES = [
  'ui_interaction_tracking',
  'progress_status_updates',
  'performance_impact_insights',
  'performance_chart_data',
  'lighthouse_seo_metrics',
  'lighthouse_pwa_metrics',
  'lighthouse_best_practices_metrics',
  'lighthouse_accessibility_metrics',
  'ai_insights',
  'ai_conversion_roadmap'
];

/**
 * Test 1: Check if all previously empty tables are now populated
 */
async function testDatabasePopulation() {
  console.log('\n🔍 Testing Database Population...');
  
  const results = {};
  let allPopulated = true;
  
  for (const tableName of EMPTY_TABLES) {
    try {
      const { data, error, count } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true })
        .eq('analysis_id', TEST_ANALYSIS_ID);
      
      if (error) {
        console.error(`❌ Error checking ${tableName}:`, error.message);
        results[tableName] = { status: 'error', count: 0, error: error.message };
        allPopulated = false;
      } else {
        const rowCount = count || 0;
        const status = rowCount > 0 ? 'populated' : 'empty';
        results[tableName] = { status, count: rowCount };
        
        if (rowCount === 0) {
          allPopulated = false;
          console.log(`❌ ${tableName}: EMPTY (0 rows)`);
        } else {
          console.log(`✅ ${tableName}: POPULATED (${rowCount} rows)`);
        }
      }
    } catch (err) {
      console.error(`❌ Exception checking ${tableName}:`, err.message);
      results[tableName] = { status: 'error', count: 0, error: err.message };
      allPopulated = false;
    }
  }
  
  console.log(`\n📊 Database Population Test: ${allPopulated ? '✅ PASSED' : '❌ FAILED'}`);
  return { passed: allPopulated, results };
}

/**
 * Test 2: Check live progress tracking system
 */
async function testLiveProgressTracking() {
  console.log('\n🔄 Testing Live Progress Tracking...');
  
  try {
    // Check analysis_generation_progress
    const { data: progressSteps, error: progressError } = await supabase
      .from('analysis_generation_progress')
      .select('*')
      .eq('analysis_id', TEST_ANALYSIS_ID)
      .order('step_number');
    
    if (progressError) {
      console.error('❌ Error fetching progress steps:', progressError.message);
      return { passed: false, error: progressError.message };
    }
    
    // Check live_progress_feed
    const { data: liveFeed, error: liveFeedError } = await supabase
      .from('live_progress_feed')
      .select('*')
      .eq('analysis_id', TEST_ANALYSIS_ID)
      .order('step_number');
    
    if (liveFeedError) {
      console.error('❌ Error fetching live progress feed:', liveFeedError.message);
      return { passed: false, error: liveFeedError.message };
    }
    
    // Check progress_status_updates
    const { data: statusUpdates, error: statusError } = await supabase
      .from('progress_status_updates')
      .select('*')
      .in('progress_feed_id', liveFeed?.map(feed => feed.id) || []);
    
    if (statusError) {
      console.error('❌ Error fetching status updates:', statusError.message);
      return { passed: false, error: statusError.message };
    }
    
    console.log(`✅ Progress Steps: ${progressSteps?.length || 0} found`);
    console.log(`✅ Live Progress Feed: ${liveFeed?.length || 0} entries found`);
    console.log(`✅ Status Updates: ${statusUpdates?.length || 0} updates found`);
    
    const hasProgressData = (progressSteps?.length || 0) > 0;
    const hasLiveFeed = (liveFeed?.length || 0) > 0;
    const hasStatusUpdates = (statusUpdates?.length || 0) > 0;
    
    const passed = hasProgressData && hasLiveFeed && hasStatusUpdates;
    console.log(`\n📊 Live Progress Tracking Test: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
    
    return {
      passed,
      progressSteps: progressSteps?.length || 0,
      liveFeed: liveFeed?.length || 0,
      statusUpdates: statusUpdates?.length || 0
    };
  } catch (err) {
    console.error('❌ Exception in live progress tracking test:', err.message);
    return { passed: false, error: err.message };
  }
}

/**
 * Test 3: Check analysis completion detection
 */
async function testAnalysisCompletion() {
  console.log('\n🎯 Testing Analysis Completion Detection...');
  
  try {
    const { data: analysis, error } = await supabase
      .from('analyses')
      .select(`
        id,
        generation_status,
        current_generation_step,
        completed_generation_steps,
        total_generation_steps,
        generation_completed_at,
        all_content_generated
      `)
      .eq('id', TEST_ANALYSIS_ID)
      .single();
    
    if (error) {
      console.error('❌ Error fetching analysis:', error.message);
      return { passed: false, error: error.message };
    }
    
    console.log(`📋 Generation Status: ${analysis.generation_status}`);
    console.log(`📋 Current Step: ${analysis.current_generation_step}`);
    console.log(`📋 Completed Steps: ${analysis.completed_generation_steps}/${analysis.total_generation_steps}`);
    console.log(`📋 All Content Generated: ${analysis.all_content_generated}`);
    console.log(`📋 Completion Time: ${analysis.generation_completed_at}`);
    
    const isCompleted = analysis.generation_status === 'completed';
    const hasCompletionTime = !!analysis.generation_completed_at;
    const allContentGenerated = analysis.all_content_generated;
    
    const passed = isCompleted && hasCompletionTime && allContentGenerated;
    console.log(`\n📊 Analysis Completion Test: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
    
    return {
      passed,
      status: analysis.generation_status,
      completedSteps: analysis.completed_generation_steps,
      totalSteps: analysis.total_generation_steps,
      hasCompletionTime,
      allContentGenerated
    };
  } catch (err) {
    console.error('❌ Exception in analysis completion test:', err.message);
    return { passed: false, error: err.message };
  }
}

/**
 * Test 4: Test API progress endpoint
 */
async function testProgressAPI() {
  console.log('\n🌐 Testing Progress API Endpoint...');
  
  try {
    const response = await fetch(`http://localhost:4321/api/analysis-progress?analysisId=${TEST_ANALYSIS_ID}`, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
    if (!response.ok) {
      console.error(`❌ API returned ${response.status}: ${response.statusText}`);
      return { passed: false, error: `HTTP ${response.status}` };
    }
    
    const data = await response.json();
    
    console.log(`✅ API Response Status: ${response.status}`);
    console.log(`✅ Analysis Status: ${data.status}`);
    console.log(`✅ Progress Percentage: ${data.progressPercentage}%`);
    console.log(`✅ Steps Count: ${data.steps?.length || 0}`);
    console.log(`✅ Live Progress: ${data.liveProgress?.hasLiveFeed ? 'Active' : 'Inactive'}`);
    console.log(`✅ Estimated Time: ${data.estimatedTimeRemaining || 'N/A'}`);
    
    const hasValidData = data.analysisId && data.status && typeof data.progressPercentage === 'number';
    const hasSteps = (data.steps?.length || 0) > 0;
    const hasLiveProgress = data.liveProgress?.hasLiveFeed;
    
    const passed = hasValidData && hasSteps;
    console.log(`\n📊 Progress API Test: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
    
    return {
      passed,
      responseStatus: response.status,
      hasValidData,
      hasSteps,
      hasLiveProgress,
      stepsCount: data.steps?.length || 0
    };
  } catch (err) {
    console.error('❌ Exception in progress API test:', err.message);
    return { passed: false, error: err.message };
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🚀 Starting ConvertIQ Critical Issues Test Suite');
  console.log(`📋 Test Analysis ID: ${TEST_ANALYSIS_ID}`);
  console.log('=' .repeat(60));
  
  const results = {
    databasePopulation: await testDatabasePopulation(),
    liveProgressTracking: await testLiveProgressTracking(),
    analysisCompletion: await testAnalysisCompletion(),
    progressAPI: await testProgressAPI()
  };
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  
  const testNames = Object.keys(results);
  const passedTests = testNames.filter(test => results[test].passed);
  const failedTests = testNames.filter(test => !results[test].passed);
  
  console.log(`✅ Passed: ${passedTests.length}/${testNames.length}`);
  console.log(`❌ Failed: ${failedTests.length}/${testNames.length}`);
  
  if (passedTests.length > 0) {
    console.log(`\n✅ Passed Tests: ${passedTests.join(', ')}`);
  }
  
  if (failedTests.length > 0) {
    console.log(`\n❌ Failed Tests: ${failedTests.join(', ')}`);
  }
  
  const allPassed = failedTests.length === 0;
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 ConvertIQ critical issues have been successfully fixed!');
    console.log('✅ Live progress tracking is working');
    console.log('✅ All database tables are populated');
    console.log('✅ Analysis completion detection is working');
    console.log('✅ Real-time progress API is functional');
  } else {
    console.log('\n⚠️  Some issues still need attention. Check the failed tests above.');
  }
  
  return allPassed;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(err => {
      console.error('💥 Test suite crashed:', err);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testDatabasePopulation,
  testLiveProgressTracking,
  testAnalysisCompletion,
  testProgressAPI
};

