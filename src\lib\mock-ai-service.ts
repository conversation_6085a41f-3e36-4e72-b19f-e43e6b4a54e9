/**
 * Mock AI service for development and testing
 * Provides realistic AI responses without requiring API keys
 */

export const mockAIResponses = {
  comprehensiveAnalysis: (url: string, content: string) => ({
    pageSummary: {
      businessType: "Search Engine",
      valueProposition: "Comprehensive web search and information discovery platform",
      targetAudience: "Global internet users seeking information",
      keyFeatures: ["Web search", "Image search", "Maps", "Email", "Cloud storage"],
      conversionGoals: ["User engagement", "Ad clicks", "Service adoption"],
      trustSignals: ["Brand recognition", "Clean design", "Fast loading"],
      painPoints: ["Information overload", "Privacy concerns", "Ad relevance"]
    },
    strengths: [
      "Globally recognized brand with high trust",
      "Clean, minimalist interface design",
      "Fast loading and responsive performance",
      "Comprehensive service ecosystem",
      "Strong mobile optimization"
    ],
    weaknesses: [
      "Limited personalization on homepage",
      "Minimal conversion optimization",
      "Basic landing page structure",
      "Limited social proof elements",
      "No clear value proposition messaging"
    ],
    targetAudience: "Global internet users across all demographics seeking information, services, and digital tools",
    conversionScore: 7.5,
    leadInsights: {
      businessType: "Technology Platform",
      businessTypeDescription: "Global search engine and technology services provider",
      valueProposition: "Organize the world's information and make it universally accessible",
      valuePropositionDescription: "Comprehensive digital services ecosystem for information discovery and productivity",
      targetAudience: "Global internet users",
      targetAudienceMarkdown: "## Target Audience Analysis\n\n### Primary Users\n- **Information Seekers**: Users looking for quick, accurate answers\n- **Business Users**: Professionals using productivity tools\n- **Mobile Users**: On-the-go information access\n\n### Demographics\n- **Age**: All age groups, with heavy usage in 18-65\n- **Geography**: Global reach with localized services\n- **Behavior**: Daily active users with high engagement",
      leadQuestions: [
        {
          question: "What information are you looking for today?",
          purpose: "Understand search intent and user needs",
          followUp: "How can we help you find it faster?"
        },
        {
          question: "Do you use other Google services?",
          purpose: "Identify cross-selling opportunities",
          followUp: "Which services would be most valuable to you?"
        }
      ],
      businessImpact: [
        "High user engagement drives advertising revenue",
        "Service ecosystem creates user retention",
        "Brand trust enables new product adoption"
      ],
      conversionBarriers: [
        "Privacy concerns may limit data sharing",
        "Service complexity can overwhelm new users",
        "Competition from specialized platforms"
      ]
    }
  }),

  prosConsAnalysis: (url: string, content: string) => ({
    strengths: [
      "Globally recognized and trusted brand",
      "Clean, intuitive user interface",
      "Fast loading and responsive design",
      "Comprehensive service ecosystem",
      "Strong mobile optimization"
    ],
    weaknesses: [
      "Limited personalization on homepage",
      "Minimal conversion optimization elements",
      "Basic landing page structure",
      "Limited social proof or testimonials",
      "No clear value proposition messaging"
    ],
    overallAssessment: "Strong technical foundation with opportunities for enhanced user engagement and conversion optimization"
  }),

  performanceAnalysis: (analysis: any) => ({
    primaryIssue: {
      title: "Performance Optimization Opportunities",
      description: "While core metrics are solid, there are opportunities for speed improvements",
      evidence: "Based on Lighthouse analysis and Core Web Vitals",
      conversionImpact: "Performance improvements could increase user engagement by 15-25%"
    },
    keyFindings: [
      "Largest Contentful Paint could be optimized for faster perceived loading",
      "Resource optimization opportunities for better efficiency",
      "Mobile performance is strong but desktop could be enhanced"
    ],
    recommendations: [
      {
        title: "Optimize Critical Rendering Path",
        description: "Prioritize above-the-fold content loading",
        expectedImprovement: "20-30% faster perceived loading",
        priority: "High"
      },
      {
        title: "Implement Resource Optimization",
        description: "Compress and optimize images and scripts",
        expectedImprovement: "15-20% reduction in load time",
        priority: "Medium"
      }
    ],
    businessImpact: "Performance improvements directly correlate with user satisfaction and engagement metrics"
  }),

  leadInsights: (analysis: any) => ({
    businessType: "Technology Platform",
    businessTypeDescription: "Global search engine and digital services provider",
    valueProposition: "Organize the world's information and make it universally accessible",
    valuePropositionDescription: "Comprehensive digital ecosystem for information discovery and productivity",
    targetAudience: "Global internet users",
    targetAudienceMarkdown: "## Target Audience Analysis\n\n### Primary Users\n- **Information Seekers**: Daily search users\n- **Business Users**: Professional productivity tools\n- **Mobile Users**: On-the-go access needs\n\n### Engagement Patterns\n- High frequency usage\n- Multi-service adoption\n- Cross-platform consistency",
    leadQuestions: [
      {
        question: "What brings you to our platform today?",
        purpose: "Understand user intent and immediate needs",
        followUp: "How can we make your experience more efficient?"
      }
    ],
    businessImpact: [
      "User engagement drives advertising revenue",
      "Service ecosystem creates retention",
      "Brand trust enables expansion"
    ],
    conversionBarriers: [
      "Privacy concerns",
      "Service complexity",
      "Platform competition"
    ]
  }),

  pageSummary: (url: string, content: string) => ({
    businessType: "Technology Platform",
    valueProposition: "Global information access and digital services",
    targetAudience: "Internet users worldwide",
    keyFeatures: ["Search", "Maps", "Email", "Cloud Storage", "Productivity Tools"],
    conversionGoals: ["User engagement", "Service adoption", "Ad interaction"],
    trustSignals: ["Brand recognition", "Clean design", "Reliability"],
    painPoints: ["Information overload", "Privacy concerns", "Service complexity"]
  })
};

export const useMockAI = import.meta.env.DEV && !import.meta.env.PUBLIC_OPENROUTER_API_KEY?.startsWith('sk-or-');
