<template>
  <div class="space-y-6">
    <!-- AI Performance Impact Analysis (Collapsible) -->
    <div class="bg-white rounded-lg border border-gray-200">
      <button
        @click="showAIAnalysis = !showAIAnalysis"
        class="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
      >
        <div class="flex items-center">
          <Brain class="w-5 h-5 text-blue-500 mr-3" />
          <h3 class="text-lg font-semibold text-gray-900">AI Performance Impact Analysis</h3>
        </div>
        <ChevronDown :class="['w-5 h-5 text-gray-400 transition-transform', showAIAnalysis ? 'rotate-180' : '']" />
      </button>
      
      <Transition name="slide-down">
        <div v-if="showAIAnalysis" class="px-6 pb-6 border-t border-gray-100">
          <div v-if="aiPerformanceInsights" class="space-y-4 mt-4">
            <!-- Primary Issue -->
            <div class="bg-red-50 rounded-lg p-4 border border-red-200">
              <h4 class="font-medium text-red-900 mb-2 flex items-center">
                <AlertTriangle class="w-4 h-4 mr-2" />
                {{ aiPerformanceInsights.primaryIssue.title }}
              </h4>
              <p class="text-red-800 text-sm mb-2">{{ aiPerformanceInsights.primaryIssue.description }}</p>
              <p class="text-red-700 text-xs"><strong>Impact:</strong> {{ aiPerformanceInsights.primaryIssue.conversionImpact }}</p>
            </div>

            <!-- Device/Browser Info -->
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <h4 class="font-medium text-blue-900 mb-2 flex items-center">
                <Monitor class="w-4 h-4 mr-2" />
                Test Environment
              </h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-blue-700 font-medium">Device:</span>
                  <span class="text-blue-800 ml-2">Mobile (Simulated)</span>
                </div>
                <div>
                  <span class="text-blue-700 font-medium">Network:</span>
                  <span class="text-blue-800 ml-2">Regular 4G</span>
                </div>
                <div>
                  <span class="text-blue-700 font-medium">Browser:</span>
                  <span class="text-blue-800 ml-2">Chrome (Latest)</span>
                </div>
                <div>
                  <span class="text-blue-700 font-medium">Location:</span>
                  <span class="text-blue-800 ml-2">US East</span>
                </div>
              </div>
            </div>

            <!-- Key Recommendations -->
            <div class="space-y-3">
              <h4 class="font-medium text-gray-900 flex items-center">
                <Lightbulb class="w-4 h-4 mr-2 text-yellow-500" />
                Priority Recommendations
              </h4>
              <div class="space-y-2">
                <div 
                  v-for="(rec, index) in aiPerformanceInsights.recommendations.slice(0, 3)" 
                  :key="index"
                  class="flex items-start p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <span class="text-xs font-medium text-blue-600">{{ index + 1 }}</span>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ rec.title }}</p>
                    <p class="text-xs text-gray-600 mt-1">{{ rec.description }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                      <span class="text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded">{{ rec.impact }} Impact</span>
                      <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">{{ rec.effort }} Effort</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- Content Breakdown Chart -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <PieChart class="w-5 h-5 text-purple-500 mr-3" />
        Content Breakdown
        <InfoBox
          content="Shows the breakdown of different content types and their impact on page load performance. This helps identify which resources are consuming the most bandwidth and affecting load times."
          title="Content Breakdown Analysis"
          position="top"
          :icon="Info"
          icon-class="w-4 h-4"
          trigger-class="ml-2 text-gray-400 hover:text-gray-600"
        />
      </h3>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Pie Chart Placeholder -->
        <div class="flex items-center justify-center h-48 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div class="text-center">
            <PieChart class="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 text-sm">Content breakdown chart</p>
            <p class="text-gray-400 text-xs">Coming soon</p>
          </div>
        </div>

        <!-- Content Types List -->
        <div class="space-y-3">
          <div 
            v-for="contentType in contentBreakdown" 
            :key="contentType.type"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center">
              <div 
                class="w-4 h-4 rounded-full mr-3"
                :style="{ backgroundColor: contentType.color }"
              ></div>
              <span class="text-sm font-medium text-gray-900">{{ contentType.type }}</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">{{ contentType.size }}</div>
              <div class="text-xs text-gray-500">{{ contentType.percentage }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- First View Analysis -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Eye class="w-5 h-5 text-green-500 mr-3" />
        First View Analysis
        <InfoBox
          content="Analysis of what users see and experience during their first visit to your page. This includes visual progression, above-the-fold content, and initial user experience metrics."
          title="First View Analysis"
          position="top"
          :icon="Info"
          icon-class="w-4 h-4"
          trigger-class="ml-2 text-gray-400 hover:text-gray-600"
        />
      </h3>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Screenshot Analysis -->
        <div class="lg:col-span-2">
          <h4 class="font-medium text-gray-900 mb-3">Visual Analysis</h4>
          <div class="bg-gray-100 rounded-lg p-4 h-48 flex items-center justify-center">
            <div class="text-center">
              <Camera class="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p class="text-gray-500 text-sm">Screenshot analysis</p>
              <p class="text-gray-400 text-xs">Visual progression timeline</p>
            </div>
          </div>
        </div>

        <!-- Key Insights -->
        <div class="space-y-4">
          <h4 class="font-medium text-gray-900">Key Insights</h4>
          <div class="space-y-3">
            <div class="p-3 bg-green-50 rounded-lg border border-green-200">
              <div class="flex items-center mb-2">
                <CheckCircle class="w-4 h-4 text-green-500 mr-2" />
                <span class="text-sm font-medium text-green-900">Above the Fold</span>
              </div>
              <p class="text-xs text-green-800">Critical content loads within {{ lighthouseData?.coreWebVitals?.lcp || 2.5 }}s</p>
            </div>
            
            <div class="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <div class="flex items-center mb-2">
                <Clock class="w-4 h-4 text-yellow-500 mr-2" />
                <span class="text-sm font-medium text-yellow-900">Time to Interactive</span>
              </div>
              <p class="text-xs text-yellow-800">Page becomes interactive in {{ (lighthouseData?.coreWebVitals?.fid || 100) }}ms</p>
            </div>

            <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div class="flex items-center mb-2">
                <Zap class="w-4 h-4 text-blue-500 mr-2" />
                <span class="text-sm font-medium text-blue-900">Layout Stability</span>
              </div>
              <p class="text-xs text-blue-800">CLS score: {{ lighthouseData?.coreWebVitals?.cls || 0.1 }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI-Powered Assessment -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <Brain class="w-5 h-5 text-purple-500 mr-3" />
        AI-Powered Assessment
      </h3>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Is it Usable? -->
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               :class="usabilityScore >= 7 ? 'bg-green-100' : usabilityScore >= 4 ? 'bg-yellow-100' : 'bg-red-100'">
            <Users :class="['w-8 h-8', usabilityScore >= 7 ? 'text-green-600' : usabilityScore >= 4 ? 'text-yellow-600' : 'text-red-600']" />
          </div>
          <h4 class="font-medium text-gray-900 mb-2">Is it Usable?</h4>
          <div class="text-2xl font-bold mb-2"
               :class="usabilityScore >= 7 ? 'text-green-600' : usabilityScore >= 4 ? 'text-yellow-600' : 'text-red-600'">
            {{ usabilityScore }}/10
          </div>
          <p class="text-sm text-gray-600">{{ usabilityAssessment }}</p>
        </div>

        <!-- Is it Quick? -->
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               :class="speedScore >= 7 ? 'bg-green-100' : speedScore >= 4 ? 'bg-yellow-100' : 'bg-red-100'">
            <Zap :class="['w-8 h-8', speedScore >= 7 ? 'text-green-600' : speedScore >= 4 ? 'text-yellow-600' : 'text-red-600']" />
          </div>
          <h4 class="font-medium text-gray-900 mb-2">Is it Quick?</h4>
          <div class="text-2xl font-bold mb-2"
               :class="speedScore >= 7 ? 'text-green-600' : speedScore >= 4 ? 'text-yellow-600' : 'text-red-600'">
            {{ speedScore }}/10
          </div>
          <p class="text-sm text-gray-600">{{ speedAssessment }}</p>
        </div>

        <!-- Is it Resilient? -->
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               :class="resilienceScore >= 7 ? 'bg-green-100' : resilienceScore >= 4 ? 'bg-yellow-100' : 'bg-red-100'">
            <Shield :class="['w-8 h-8', resilienceScore >= 7 ? 'text-green-600' : resilienceScore >= 4 ? 'text-yellow-600' : 'text-red-600']" />
          </div>
          <h4 class="font-medium text-gray-900 mb-2">Is it Resilient?</h4>
          <div class="text-2xl font-bold mb-2"
               :class="resilienceScore >= 7 ? 'text-green-600' : resilienceScore >= 4 ? 'text-yellow-600' : 'text-red-600'">
            {{ resilienceScore }}/10
          </div>
          <p class="text-sm text-gray-600">{{ resilienceAssessment }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  Brain,
  ChevronDown,
  AlertTriangle,
  Monitor,
  Lightbulb,
  PieChart,
  Info,
  Eye,
  Camera,
  CheckCircle,
  Clock,
  Zap,
  Users,
  Shield
} from 'lucide-vue-next';
import InfoBox from '../ui/InfoBox.vue';
import { useInfoContent } from '../../composables/useInfoContent';

const props = defineProps<{
  lighthouseData: any;
  aiPerformanceInsights: any;
}>();

const showAIAnalysis = ref(false);

// Info content system
const {
  getContentText,
  getContentTitle,
  getPositionHint,
  getTriggerEvent,
  trackInteraction,
  preloadCommonContent
} = useInfoContent();

// Content breakdown data (mock data for now)
const contentBreakdown = ref([
  { type: 'Images', size: '1.2 MB', percentage: 45, color: '#3B82F6' },
  { type: 'JavaScript', size: '680 KB', percentage: 25, color: '#F59E0B' },
  { type: 'CSS', size: '320 KB', percentage: 12, color: '#10B981' },
  { type: 'HTML', size: '180 KB', percentage: 7, color: '#8B5CF6' },
  { type: 'Fonts', size: '150 KB', percentage: 6, color: '#EF4444' },
  { type: 'Other', size: '120 KB', percentage: 5, color: '#6B7280' }
]);

// AI Assessment Scores
const usabilityScore = computed(() => {
  const score = props.lighthouseData?.accessibilityScore || 0;
  return Math.round(score / 10);
});

const speedScore = computed(() => {
  const score = props.lighthouseData?.performanceScore || 0;
  return Math.round(score / 10);
});

const resilienceScore = computed(() => {
  const score = props.lighthouseData?.bestPracticesScore || 0;
  return Math.round(score / 10);
});

const usabilityAssessment = computed(() => {
  if (usabilityScore.value >= 8) return 'Excellent user experience';
  if (usabilityScore.value >= 6) return 'Good usability with minor issues';
  if (usabilityScore.value >= 4) return 'Moderate usability concerns';
  return 'Significant usability improvements needed';
});

const speedAssessment = computed(() => {
  if (speedScore.value >= 8) return 'Fast loading experience';
  if (speedScore.value >= 6) return 'Acceptable performance';
  if (speedScore.value >= 4) return 'Slow loading times';
  return 'Critical performance issues';
});

const resilienceAssessment = computed(() => {
  if (resilienceScore.value >= 8) return 'Robust and reliable';
  if (resilienceScore.value >= 6) return 'Generally stable';
  if (resilienceScore.value >= 4) return 'Some stability concerns';
  return 'Reliability issues detected';
});

onMounted(async () => {
  await preloadCommonContent();
});
</script>

<style scoped>
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 1000px;
  opacity: 1;
}
</style>
