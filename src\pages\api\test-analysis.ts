import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { log } from '../../lib/logger';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId } = await request.json();
    
    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'analysisId is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    log.info(`Testing analysis trigger for: ${analysisId}`);

    // Get the analysis
    const { data: analysis, error: fetchError } = await supabaseAdmin
      .from('analyses')
      .select('*')
      .eq('id', analysisId)
      .single();

    if (fetchError || !analysis) {
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Trigger comprehensive analysis
    const baseUrl = new URL(request.url).origin;
    const response = await fetch(`${baseUrl}/api/comprehensive-analysis`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: analysis.url,
        analysisId: analysis.id
      })
    });

    const result = await response.json();

    return new Response(JSON.stringify({
      success: true,
      analysisId,
      url: analysis.url,
      comprehensiveAnalysisResult: result,
      status: response.status
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error testing analysis: ${error}`);
    return new Response(JSON.stringify({
      error: 'Test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
