<template>
  <div class="relative inline-block">
    <!-- Trigger Element -->
    <button
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
      @focus="handleFocus"
      @blur="handleBlur"
      :class="[
        'inline-flex items-center justify-center transition-colors duration-200',
        triggerClass
      ]"
      :aria-describedby="isVisible ? tooltipId : undefined"
      :aria-expanded="isVisible"
    >
      <component 
        :is="icon" 
        :class="[
          'transition-colors duration-200',
          iconClass,
          isVisible ? activeIconClass : inactiveIconClass
        ]" 
      />
    </button>

    <!-- Tooltip Content -->
    <Teleport to="body" :disabled="typeof window === 'undefined'">
      <div
        v-if="isVisible"
        :id="tooltipId"
        ref="tooltipRef"
        :class="[
          'absolute z-50 px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg shadow-lg transition-all duration-200 ease-out',
          'max-w-xs sm:max-w-sm md:max-w-md',
          positionClass,
          isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
        ]"
        :style="tooltipStyle"
        role="tooltip"
        @mouseenter="handleTooltipMouseEnter"
        @mouseleave="handleTooltipMouseLeave"
      >
        <!-- Title -->
        <div v-if="title" class="font-medium text-gray-900 mb-1">
          {{ title }}
        </div>
        
        <!-- Content -->
        <div class="text-gray-700 leading-relaxed">
          <div v-if="isHtml" v-html="content"></div>
          <div v-else>{{ content }}</div>
        </div>

        <!-- Arrow -->
        <div
          :class="[
            'absolute w-2 h-2 bg-white border transform rotate-45',
            arrowClass
          ]"
        ></div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';
import { Info } from 'lucide-vue-next';

interface Props {
  content: string;
  title?: string;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  trigger?: 'hover' | 'click' | 'both';
  icon?: any;
  iconClass?: string;
  triggerClass?: string;
  delay?: number;
  isHtml?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  position: 'auto',
  trigger: 'hover',
  icon: Info,
  iconClass: 'w-4 h-4',
  triggerClass: 'p-1 text-gray-400 hover:text-gray-600 rounded',
  delay: 300,
  isHtml: false,
  disabled: false
});

const isVisible = ref(false);
const tooltipRef = ref<HTMLElement>();
const tooltipStyle = ref({});
const actualPosition = ref<'top' | 'bottom' | 'left' | 'right'>('top');
const hoverTimeout = ref<number>();
const hideTimeout = ref<number>();

const tooltipId = computed(() => `tooltip-${Math.random().toString(36).substr(2, 9)}`);

const activeIconClass = computed(() => 'text-blue-600');
const inactiveIconClass = computed(() => 'text-gray-400');

const positionClass = computed(() => {
  const classes = {
    top: 'mb-2',
    bottom: 'mt-2', 
    left: 'mr-2',
    right: 'ml-2'
  };
  return classes[actualPosition.value];
});

const arrowClass = computed(() => {
  const classes = {
    top: 'top-full left-1/2 -translate-x-1/2 -mt-1 border-t-gray-200 border-r-gray-200',
    bottom: 'bottom-full left-1/2 -translate-x-1/2 -mb-1 border-b-gray-200 border-l-gray-200',
    left: 'left-full top-1/2 -translate-y-1/2 -ml-1 border-t-gray-200 border-l-gray-200',
    right: 'right-full top-1/2 -translate-y-1/2 -mr-1 border-b-gray-200 border-r-gray-200'
  };
  return classes[actualPosition.value];
});

const clearTimeouts = () => {
  if (hoverTimeout.value) {
    clearTimeout(hoverTimeout.value);
    hoverTimeout.value = undefined;
  }
  if (hideTimeout.value) {
    clearTimeout(hideTimeout.value);
    hideTimeout.value = undefined;
  }
};

const calculatePosition = async () => {
  if (!tooltipRef.value || typeof window === 'undefined') return;

  await nextTick();

  const trigger = tooltipRef.value.previousElementSibling as HTMLElement;
  if (!trigger) return;

  const triggerRect = trigger.getBoundingClientRect();
  const tooltipRect = tooltipRef.value.getBoundingClientRect();
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  };

  let position = props.position;
  let top = 0;
  let left = 0;

  // Auto-detect best position if needed
  if (position === 'auto') {
    const spaceTop = triggerRect.top;
    const spaceBottom = viewport.height - triggerRect.bottom;
    const spaceLeft = triggerRect.left;
    const spaceRight = viewport.width - triggerRect.right;

    if (spaceTop >= tooltipRect.height + 10) {
      position = 'top';
    } else if (spaceBottom >= tooltipRect.height + 10) {
      position = 'bottom';
    } else if (spaceRight >= tooltipRect.width + 10) {
      position = 'right';
    } else if (spaceLeft >= tooltipRect.width + 10) {
      position = 'left';
    } else {
      position = 'bottom'; // fallback
    }
  }

  actualPosition.value = position;

  // Calculate position based on determined placement
  switch (position) {
    case 'top':
      top = triggerRect.top - tooltipRect.height - 8;
      left = triggerRect.left + (triggerRect.width / 2) - (tooltipRect.width / 2);
      break;
    case 'bottom':
      top = triggerRect.bottom + 8;
      left = triggerRect.left + (triggerRect.width / 2) - (tooltipRect.width / 2);
      break;
    case 'left':
      top = triggerRect.top + (triggerRect.height / 2) - (tooltipRect.height / 2);
      left = triggerRect.left - tooltipRect.width - 8;
      break;
    case 'right':
      top = triggerRect.top + (triggerRect.height / 2) - (tooltipRect.height / 2);
      left = triggerRect.right + 8;
      break;
  }

  // Ensure tooltip stays within viewport
  left = Math.max(8, Math.min(left, viewport.width - tooltipRect.width - 8));
  top = Math.max(8, Math.min(top, viewport.height - tooltipRect.height - 8));

  tooltipStyle.value = {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    zIndex: 9999
  };
};

const showTooltip = async () => {
  if (props.disabled) return;
  
  clearTimeouts();
  isVisible.value = true;
  await nextTick();
  await calculatePosition();
};

const hideTooltip = () => {
  clearTimeouts();
  hideTimeout.value = window.setTimeout(() => {
    isVisible.value = false;
  }, 100);
};

const handleMouseEnter = () => {
  if (props.trigger === 'hover' || props.trigger === 'both') {
    clearTimeouts();
    hoverTimeout.value = window.setTimeout(showTooltip, props.delay);
  }
};

const handleMouseLeave = () => {
  if (props.trigger === 'hover' || props.trigger === 'both') {
    clearTimeouts();
    hideTooltip();
  }
};

const handleClick = () => {
  if (props.trigger === 'click' || props.trigger === 'both') {
    if (isVisible.value) {
      hideTooltip();
    } else {
      showTooltip();
    }
  }
};

const handleFocus = () => {
  if (props.trigger === 'hover' || props.trigger === 'both') {
    showTooltip();
  }
};

const handleBlur = () => {
  if (props.trigger === 'hover' || props.trigger === 'both') {
    hideTooltip();
  }
};

const handleTooltipMouseEnter = () => {
  clearTimeouts();
};

const handleTooltipMouseLeave = () => {
  if (props.trigger === 'hover' || props.trigger === 'both') {
    hideTooltip();
  }
};

const handleClickOutside = (event: Event) => {
  if (props.trigger === 'click' || props.trigger === 'both') {
    const target = event.target as HTMLElement;
    if (tooltipRef.value && !tooltipRef.value.contains(target) && 
        !tooltipRef.value.previousElementSibling?.contains(target)) {
      hideTooltip();
    }
  }
};

const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isVisible.value) {
    hideTooltip();
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  document.addEventListener('keydown', handleEscape);
  window.addEventListener('resize', calculatePosition);
  window.addEventListener('scroll', calculatePosition);
});

onUnmounted(() => {
  clearTimeouts();
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('keydown', handleEscape);
  window.removeEventListener('resize', calculatePosition);
  window.removeEventListener('scroll', calculatePosition);
});
</script>

<style scoped>
/* Smooth animations */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure tooltip appears above other elements */
[role="tooltip"] {
  z-index: 9999 !important;
}
</style>
