import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { log } from '../../lib/logger';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const analysisId = url.searchParams.get('analysisId');

    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'Missing analysisId parameter' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }

    // Get analysis with simplified progress fields using current_process
    const { data: analysis, error: analysisError } = await supabaseAdmin
      .from('analyses')
      .select('id, current_process, is_completed, generation_status, generation_error, created_at, updated_at')
      .eq('id', analysisId)
      .single();

    if (analysisError || !analysis) {
      log.error(`Analysis not found: ${analysisId}`, analysisError);
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Determine current status and completion using current_process field
    const currentProcess = analysis.current_process || 0;
    const isCompleted = analysis.is_completed || false;
    const hasError = analysis.generation_status === 'failed';

    // Calculate progress percentage (0-100) based on current_process
    const totalSteps = 9; // Total number of analysis steps (0-8)
    let progressPercentage = 0;

    if (isCompleted) {
      progressPercentage = 100;
    } else if (currentProcess >= 0) {
      // Process N means process N is in progress
      progressPercentage = Math.min(95, Math.round(((currentProcess + 1) / totalSteps) * 100));
    }

    // Determine status message based on current_process
    let statusMessage = 'Initializing analysis...';
    if (isCompleted) {
      statusMessage = 'Analysis Complete!';
    } else if (hasError) {
      statusMessage = 'Analysis failed';
    } else if (currentProcess >= 0) {
      const processNames = [
        'Initializing Analysis',
        'Generating Page Summary',
        'Analyzing Pros & Cons',
        'Lead Insights Analysis',
        'Performance Analysis',
        'SEO & Technical Analysis',
        'AI Performance Impact',
        'Chat Context Generation',
        'Finalizing Analysis'
      ];
      statusMessage = processNames[currentProcess] || 'Processing...';
    }

    // Simple response with current progress using current_process field
    const response = {
      analysisId: analysis.id,
      status: isCompleted ? 'completed' : (hasError ? 'failed' : 'in_progress'),
      statusMessage,
      currentProcess, // Updated to use current_process
      currentStep: currentProcess, // Keep for backward compatibility
      progressPercentage,
      completedSteps: Math.max(0, currentProcess),
      totalSteps,
      isComplete: isCompleted,
      hasError,
      errorMessage: analysis.generation_error || null,
      lastUpdated: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (err) {
    console.error('Error fetching progress:', err);
    return new Response(JSON.stringify({
      error: err instanceof Error ? err.message : 'Failed to fetch progress'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// POST endpoint for updating progress using current_process field
export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId, currentProcess, currentStep, isCompleted, errorMessage } = await request.json();

    if (!analysisId) {
      return new Response(JSON.stringify({
        error: 'Missing required parameter: analysisId'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update progress in analyses table using current_process field
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Prioritize current_process, fallback to currentStep for backward compatibility
    if (currentProcess !== undefined) {
      updateData.current_process = currentProcess;
    } else if (currentStep !== undefined) {
      updateData.current_process = currentStep;
    }

    if (isCompleted !== undefined) {
      updateData.is_completed = isCompleted;
    }

    if (errorMessage) {
      updateData.generation_error = errorMessage;
      updateData.generation_status = 'failed';
    }

    const { error } = await supabaseAdmin
      .from('analyses')
      .update(updateData)
      .eq('id', analysisId);

    if (error) {
      throw error;
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error updating analysis progress: ${error}`);

    return new Response(JSON.stringify({
      error: 'Failed to update analysis progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
