import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { log } from '../../lib/logger';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const analysisId = url.searchParams.get('analysisId');

    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'Missing analysisId parameter' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }

    // Get analysis with simplified progress fields
    const { data: analysis, error: analysisError } = await supabaseAdmin
      .from('analyses')
      .select('id, current_step, is_completed, generation_status, generation_error, created_at, updated_at')
      .eq('id', analysisId)
      .single();

    if (analysisError || !analysis) {
      log.error(`Analysis not found: ${analysisId}`, analysisError);
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Determine current status and completion
    const currentStep = analysis.current_step || 0;
    const isCompleted = analysis.is_completed || false;
    const hasError = analysis.generation_status === 'failed';

    // Calculate progress percentage (0-100)
    const totalSteps = 9; // Total number of analysis steps
    let progressPercentage = 0;

    if (isCompleted) {
      progressPercentage = 100;
    } else if (currentStep > 0) {
      // Step N means steps 1 to N-1 are complete, step N is in progress
      const completedSteps = Math.max(0, currentStep - 1);
      const progressInCurrentStep = 0.5; // Assume 50% progress in current step
      progressPercentage = Math.min(100, Math.round(((completedSteps + progressInCurrentStep) / totalSteps) * 100));
    }

    // Determine status message
    let statusMessage = 'Initializing analysis...';
    if (isCompleted) {
      statusMessage = 'Analysis Complete!';
    } else if (hasError) {
      statusMessage = 'Analysis failed';
    } else if (currentStep > 0) {
      const stepNames = [
        'Initializing Analysis',
        'Generating Page Summary',
        'Analyzing Pros & Cons',
        'Lead Insights Analysis',
        'Performance Analysis',
        'SEO & Technical Analysis',
        'AI Performance Impact',
        'Chat Context Generation',
        'Finalizing Analysis'
      ];
      statusMessage = stepNames[currentStep - 1] || 'Processing...';
    }

    // Simple response with current progress
    const response = {
      analysisId: analysis.id,
      status: isCompleted ? 'completed' : (hasError ? 'failed' : 'in_progress'),
      statusMessage,
      currentStep,
      progressPercentage,
      completedSteps: Math.max(0, currentStep - 1),
      totalSteps,
      isComplete: isCompleted,
      hasError,
      errorMessage: analysis.generation_error || null,
      lastUpdated: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (err) {
    console.error('Error fetching progress:', err);
    return new Response(JSON.stringify({
      error: err instanceof Error ? err.message : 'Failed to fetch progress'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// POST endpoint for updating progress (simplified)
export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId, currentStep, isCompleted, errorMessage } = await request.json();

    if (!analysisId) {
      return new Response(JSON.stringify({
        error: 'Missing required parameter: analysisId'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update progress in analyses table
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (currentStep !== undefined) {
      updateData.current_step = currentStep;
    }

    if (isCompleted !== undefined) {
      updateData.is_completed = isCompleted;
    }

    if (errorMessage) {
      updateData.generation_error = errorMessage;
      updateData.generation_status = 'failed';
    }

    const { error } = await supabaseAdmin
      .from('analyses')
      .update(updateData)
      .eq('id', analysisId);

    if (error) {
      throw error;
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error updating analysis progress: ${error}`);

    return new Response(JSON.stringify({
      error: 'Failed to update analysis progress',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
