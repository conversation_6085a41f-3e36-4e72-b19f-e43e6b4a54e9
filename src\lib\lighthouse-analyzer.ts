import { log } from './logger';
import { supabaseAdmin } from './supabase-client';

export interface LighthouseMetrics {
  performanceScore: number;
  accessibilityScore: number;
  bestPracticesScore: number;
  seoScore: number;
  coreWebVitals: {
    lcp: number;
    fid: number;
    cls: number;
  };
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  conversionImpact: string;
  recommendations: Array<{
    title: string;
    description: string;
    impact: 'High' | 'Medium' | 'Low';
    effort: 'High' | 'Medium' | 'Low';
    category: string;
  }>;
}

export interface SEOAnalysisResult {
  score: number;
  issues: Array<{
    issue: string;
    recommendation: string;
    severity_score: number;
  }>;
  data: {
    title?: string;
    metaDescription?: string;
    canonicalUrl?: string;
    openGraphTags?: Record<string, string>;
    structuredData?: any[];
    headingStructure?: Array<{ level: number; text: string }>;
    keywordDensity?: Record<string, number>;
  };
  pros: string[];
  cons: string[];
}

// Core Web Vitals thresholds (Google standards)
export const CORE_WEB_VITALS_THRESHOLDS = {
  lcp: { good: 2.5, poor: 4.0 }, // seconds
  fid: { good: 100, poor: 300 }, // milliseconds
  cls: { good: 0.1, poor: 0.25 } // score
};

export function evaluateMetric(value: number, thresholds: { good: number; poor: number }): 'good' | 'needs-improvement' | 'poor' {
  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
}

function calculateGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {
  if (score >= 90) return 'A';
  if (score >= 80) return 'B';
  if (score >= 70) return 'C';
  if (score >= 60) return 'D';
  return 'F';
}

function getConversionImpactMessage(score: number): string {
  if (score >= 90) return 'Excellent performance with potential for 2-5% conversion improvement';
  if (score >= 80) return 'Good performance with potential for 5-15% conversion improvement';
  if (score >= 70) return 'Fair performance with potential for 15-25% conversion improvement';
  if (score >= 60) return 'Poor performance with potential for 25-40% conversion improvement';
  return 'Critical performance issues with potential for 40%+ conversion improvement';
}

/**
 * Save lighthouse analysis results to database
 */
export async function saveLighthouseDataToDatabase(
  analysisId: string,
  lighthouseResult: any,
  metrics: LighthouseMetrics
): Promise<string> {
  try {
    log.info(`Saving lighthouse data to database for analysis ${analysisId}`);

    // Create audit run record
    const { data: auditRun, error: auditError } = await supabaseAdmin
      .from('lighthouse_audit_runs')
      .insert({
        analysis_id: analysisId,
        audit_timestamp: new Date().toISOString(),
        lighthouse_version: lighthouseResult.lhr?.lighthouseVersion || 'unknown',
        chrome_version: lighthouseResult.lhr?.environment?.hostUserAgent || 'unknown',
        device_type: 'mobile',
        network_throttling: 'Regular 4G',
        cpu_throttling: 1,
        audit_duration_ms: lighthouseResult.lhr?.timing?.total || 0,
        audit_status: 'completed',
        raw_lighthouse_json: lighthouseResult.lhr
      })
      .select()
      .single();

    if (auditError) {
      throw new Error(`Failed to create audit run: ${auditError.message}`);
    }

    const auditRunId = auditRun.id;

    // Save enhanced performance metrics with TTFB and advanced metrics
    const { error: perfError } = await supabaseAdmin
      .from('lighthouse_performance_metrics')
      .insert({
        audit_run_id: auditRunId,
        analysis_id: analysisId,
        largest_contentful_paint_ms: lighthouseResult.lhr?.audits?.['largest-contentful-paint']?.numericValue || 0,
        first_input_delay_ms: lighthouseResult.lhr?.audits?.['max-potential-fid']?.numericValue || 0,
        cumulative_layout_shift: lighthouseResult.lhr?.audits?.['cumulative-layout-shift']?.numericValue || 0,
        first_contentful_paint_ms: lighthouseResult.lhr?.audits?.['first-contentful-paint']?.numericValue || 0,
        speed_index_ms: lighthouseResult.lhr?.audits?.['speed-index']?.numericValue || 0,
        time_to_interactive_ms: lighthouseResult.lhr?.audits?.['interactive']?.numericValue || 0,
        time_to_first_byte_ms: lighthouseResult.lhr?.audits?.['server-response-time']?.numericValue || 0,
        server_response_time_ms: lighthouseResult.lhr?.audits?.['server-response-time']?.numericValue || 0,
        render_blocking_resources: lighthouseResult.lhr?.audits?.['render-blocking-resources']?.details?.items || [],
        unused_css_rules: lighthouseResult.lhr?.audits?.['unused-css-rules']?.details?.items || [],
        image_optimization_opportunities: lighthouseResult.lhr?.audits?.['uses-optimized-images']?.details?.items || [],
        network_requests_count: lighthouseResult.lhr?.audits?.['network-requests']?.details?.items?.length || 0,
        total_transfer_size_bytes: lighthouseResult.lhr?.audits?.['total-byte-weight']?.numericValue || 0,
        main_thread_work_breakdown: lighthouseResult.lhr?.audits?.['main-thread-tasks']?.details || {},
        third_party_summary: lighthouseResult.lhr?.audits?.['third-party-summary']?.details || {},
        resource_summary: lighthouseResult.lhr?.audits?.['resource-summary']?.details || {},
        first_meaningful_paint_ms: lighthouseResult.lhr?.audits?.['first-meaningful-paint']?.numericValue || 0,
        total_blocking_time_ms: lighthouseResult.lhr?.audits?.['total-blocking-time']?.numericValue || 0,
        max_potential_fid_ms: lighthouseResult.lhr?.audits?.['max-potential-fid']?.numericValue || 0,
        layout_shift_events: lighthouseResult.lhr?.audits?.['cumulative-layout-shift']?.details?.items || [],
        total_byte_weight: lighthouseResult.lhr?.audits?.['total-byte-weight']?.numericValue || 0,
        dom_size: lighthouseResult.lhr?.audits?.['dom-size']?.numericValue || 0,
        critical_request_chains: lighthouseResult.lhr?.audits?.['critical-request-chains']?.details || {},
        performance_score: metrics.performanceScore,
        opportunities: lighthouseResult.lhr?.audits ? Object.values(lighthouseResult.lhr.audits)
          .filter((audit: any) => audit.scoreDisplayMode === 'numeric' && audit.score < 1)
          .map((audit: any) => ({
            id: audit.id,
            title: audit.title,
            description: audit.description,
            score: audit.score,
            numericValue: audit.numericValue,
            displayValue: audit.displayValue
          })) : [],
        diagnostics: lighthouseResult.lhr?.audits ? Object.values(lighthouseResult.lhr.audits)
          .filter((audit: any) => audit.scoreDisplayMode === 'informative')
          .map((audit: any) => ({
            id: audit.id,
            title: audit.title,
            description: audit.description,
            displayValue: audit.displayValue
          })) : []
      });

    if (perfError) {
      throw new Error(`Failed to save performance metrics: ${perfError.message}`);
    }

    // Save advanced performance metrics for WebPageTest.org-style analysis
    const networkRequests = lighthouseResult.lhr?.audits?.['network-requests']?.details?.items || [];

    // Calculate resource breakdown
    let htmlRequests = 0, htmlBytes = 0, cssRequests = 0, cssBytes = 0;
    let jsRequests = 0, jsBytes = 0, imageRequests = 0, imageBytes = 0;
    let fontRequests = 0, fontBytes = 0;

    networkRequests.forEach((request: any) => {
      const resourceType = request.resourceType || 'other';
      const transferSize = request.transferSize || 0;

      switch (resourceType) {
        case 'Document':
          htmlRequests++;
          htmlBytes += transferSize;
          break;
        case 'Stylesheet':
          cssRequests++;
          cssBytes += transferSize;
          break;
        case 'Script':
          jsRequests++;
          jsBytes += transferSize;
          break;
        case 'Image':
          imageRequests++;
          imageBytes += transferSize;
          break;
        case 'Font':
          fontRequests++;
          fontBytes += transferSize;
          break;
      }
    });

    const { error: advancedPerfError } = await supabaseAdmin
      .from('advanced_performance_metrics')
      .upsert({
        analysis_id: analysisId,
        time_to_first_byte_ms: lighthouseResult.lhr?.audits?.['server-response-time']?.numericValue || 0,
        total_requests: networkRequests.length,
        total_bytes: lighthouseResult.lhr?.audits?.['total-byte-weight']?.numericValue || 0,
        html_requests: htmlRequests,
        html_bytes: htmlBytes,
        css_requests: cssRequests,
        css_bytes: cssBytes,
        js_requests: jsRequests,
        js_bytes: jsBytes,
        image_requests: imageRequests,
        image_bytes: imageBytes,
        font_requests: fontRequests,
        font_bytes: fontBytes,
        dom_content_loaded_ms: lighthouseResult.lhr?.audits?.['interactive']?.numericValue || 0,
        speed_index_ms: lighthouseResult.lhr?.audits?.['speed-index']?.numericValue || 0,
        ai_performance_score: Math.round((lighthouseResult.lhr?.categories?.performance?.score || 0) * 10),
        ai_optimization_priority: lighthouseResult.lhr?.categories?.performance?.score < 0.5 ? 'High' :
                                 lighthouseResult.lhr?.categories?.performance?.score < 0.8 ? 'Medium' : 'Low',
        ai_performance_summary: `Performance score: ${Math.round((lighthouseResult.lhr?.categories?.performance?.score || 0) * 100)}%. ${
          lighthouseResult.lhr?.categories?.performance?.score < 0.5 ? 'Significant optimization needed.' :
          lighthouseResult.lhr?.categories?.performance?.score < 0.8 ? 'Moderate optimization opportunities.' :
          'Good performance with minor optimization opportunities.'
        }`
      });

    if (advancedPerfError) {
      log.warn(`Failed to save advanced performance metrics: ${advancedPerfError.message}`);
    }

    // Save accessibility metrics
    const { error: accessError } = await supabaseAdmin
      .from('lighthouse_accessibility_metrics')
      .insert({
        audit_run_id: auditRunId,
        analysis_id: analysisId,
        accessibility_score: metrics.accessibilityScore,
        color_contrast_issues: lighthouseResult.lhr?.audits?.['color-contrast']?.details?.items || [],
        missing_alt_text_count: lighthouseResult.lhr?.audits?.['image-alt']?.details?.items?.length || 0,
        aria_issues_count: lighthouseResult.lhr?.audits ? Object.keys(lighthouseResult.lhr.audits)
          .filter(key => key.startsWith('aria-') && lighthouseResult.lhr.audits[key].score < 1)
          .length : 0,
        keyboard_navigation_issues: lighthouseResult.lhr?.audits?.['focusable-controls']?.details?.items || []
      });

    if (accessError) {
      log.warn(`Failed to save accessibility metrics: ${accessError.message}`);
    }

    // Save SEO metrics
    const { error: seoError } = await supabaseAdmin
      .from('lighthouse_seo_metrics')
      .insert({
        audit_run_id: auditRunId,
        analysis_id: analysisId,
        seo_score: metrics.seoScore,
        meta_description_present: lighthouseResult.lhr?.audits?.['meta-description']?.score === 1,
        title_tag_present: lighthouseResult.lhr?.audits?.['document-title']?.score === 1,
        h1_tag_present: lighthouseResult.lhr?.audits?.['heading-order']?.score === 1 || false,
        canonical_url_present: lighthouseResult.lhr?.audits?.['canonical']?.score === 1,
        robots_txt_valid: lighthouseResult.lhr?.audits?.['robots-txt']?.score === 1,
        structured_data_present: lighthouseResult.lhr?.audits?.['structured-data']?.score === 1,
        title_tag_length: lighthouseResult.lhr?.audits?.['document-title']?.displayValue?.length || 0,
        meta_description_length: lighthouseResult.lhr?.audits?.['meta-description']?.displayValue?.length || 0,
        h1_count: lighthouseResult.lhr?.audits?.['heading-order']?.details?.items?.filter((item: any) => item.level === 1)?.length || 0
      });

    if (seoError) {
      log.warn(`Failed to save SEO metrics: ${seoError.message}`);
    }

    // Save best practices metrics
    const { error: bpError } = await supabaseAdmin
      .from('lighthouse_best_practices_metrics')
      .insert({
        audit_run_id: auditRunId,
        analysis_id: analysisId,
        best_practices_score: metrics.bestPracticesScore,
        https_usage: lighthouseResult.lhr?.audits?.['is-on-https']?.score === 1,
        console_errors_count: lighthouseResult.lhr?.audits?.['errors-in-console']?.details?.items?.length || 0,
        deprecated_apis_count: lighthouseResult.lhr?.audits?.['deprecations']?.details?.items?.length || 0,
        vulnerable_libraries_count: lighthouseResult.lhr?.audits?.['no-vulnerable-libraries']?.details?.items?.length || 0
      });

    if (bpError) {
      log.warn(`Failed to save best practices metrics: ${bpError.message}`);
    }

    // Save performance chart data
    await savePerformanceChartData(analysisId, lighthouseResult, metrics);

    // Save advanced metrics summary
    await saveAdvancedMetricsSummary(analysisId, lighthouseResult, metrics);

    log.info(`Successfully saved lighthouse data to database for analysis ${analysisId}`);
    return auditRunId;

  } catch (error) {
    log.error(`Error saving lighthouse data to database: ${error}`);
    throw error;
  }
}

/**
 * Save performance chart data for visualization
 */
async function savePerformanceChartData(analysisId: string, lighthouseResult: any, metrics: LighthouseMetrics): Promise<void> {
  try {
    const lhr = lighthouseResult.lhr;

    // Breakdown chart data
    const breakdownData = {
      labels: ['JavaScript', 'Images', 'CSS', 'Fonts', 'Other'],
      datasets: [{
        label: 'Resource Impact (%)',
        data: [
          Math.round((lhr?.audits?.['unused-javascript']?.details?.overallSavingsMs || 0) / 10),
          Math.round((lhr?.audits?.['unused-css-rules']?.details?.overallSavingsMs || 0) / 10),
          Math.round((lhr?.audits?.['render-blocking-resources']?.details?.overallSavingsMs || 0) / 10),
          Math.round((lhr?.audits?.['font-display']?.details?.overallSavingsMs || 0) / 5),
          15 // Other resources
        ],
        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
      }]
    };

    // Timeline chart data
    const timelineData = {
      labels: ['First Paint', 'First Contentful Paint', 'Largest Contentful Paint', 'Time to Interactive'],
      datasets: [{
        label: 'Load Time (seconds)',
        data: [
          (lhr?.audits?.['first-contentful-paint']?.numericValue || 0) / 1000 * 0.5,
          (lhr?.audits?.['first-contentful-paint']?.numericValue || 0) / 1000,
          (lhr?.audits?.['largest-contentful-paint']?.numericValue || 0) / 1000,
          (lhr?.audits?.['interactive']?.numericValue || 0) / 1000
        ],
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true
      }]
    };

    // Save chart data
    const chartDataToInsert = [
      {
        analysis_id: analysisId,
        chart_type: 'breakdown',
        chart_data: breakdownData,
        chart_config: { type: 'doughnut', cutout: '60%' }
      },
      {
        analysis_id: analysisId,
        chart_type: 'timeline',
        chart_data: timelineData,
        chart_config: { type: 'line', responsive: true }
      }
    ];

    await supabaseAdmin
      .from('performance_chart_data')
      .upsert(chartDataToInsert);

    log.info(`Saved performance chart data for analysis ${analysisId}`);
  } catch (error) {
    log.error(`Error saving performance chart data: ${error}`);
  }
}

/**
 * Save advanced metrics summary
 */
async function saveAdvancedMetricsSummary(analysisId: string, lighthouseResult: any, metrics: LighthouseMetrics): Promise<void> {
  try {
    const lhr = lighthouseResult.lhr;

    const advancedSummary = {
      analysis_id: analysisId,
      lighthouse_summary: `Performance score: ${metrics.performanceScore}/100. Core Web Vitals: LCP ${metrics.coreWebVitals.lcp}s, FID ${metrics.coreWebVitals.fid}ms, CLS ${metrics.coreWebVitals.cls}`,
      performance_breakdown: {
        coreWebVitals: metrics.coreWebVitals,
        timingMetrics: {
          firstContentfulPaint: lhr?.audits?.['first-contentful-paint']?.numericValue || 0,
          speedIndex: lhr?.audits?.['speed-index']?.numericValue || 0,
          timeToInteractive: lhr?.audits?.['interactive']?.numericValue || 0,
          totalBlockingTime: lhr?.audits?.['total-blocking-time']?.numericValue || 0,
          firstMeaningfulPaint: lhr?.audits?.['first-meaningful-paint']?.numericValue || 0,
          maxPotentialFid: lhr?.audits?.['max-potential-fid']?.numericValue || 0
        },
        networkMetrics: {
          ttfb: lhr?.audits?.['server-response-time']?.numericValue || 0,
          totalRequests: lhr?.audits?.['network-requests']?.details?.items?.length || 0,
          totalTransferSize: lhr?.audits?.['total-byte-weight']?.numericValue || 0,
          domContentLoaded: lhr?.audits?.['dom-size']?.numericValue || 0
        },
        advancedMetrics: {
          renderBlockingResources: lhr?.audits?.['render-blocking-resources']?.details?.items?.length || 0,
          unusedCssRules: lhr?.audits?.['unused-css-rules']?.details?.overallSavingsBytes || 0,
          unusedJavaScript: lhr?.audits?.['unused-javascript']?.details?.overallSavingsBytes || 0,
          modernImageFormats: lhr?.audits?.['modern-image-formats']?.details?.items?.length || 0,
          offscreenImages: lhr?.audits?.['offscreen-images']?.details?.items?.length || 0
        }
      },
      accessibility_insights: `Accessibility score: ${metrics.accessibilityScore}/100. Key issues: color contrast, missing alt text, ARIA implementation.`,
      best_practices_analysis: `Best practices score: ${metrics.bestPracticesScore}/100. HTTPS usage, console errors, and deprecated APIs checked.`,
      seo_technical_summary: `SEO score: ${metrics.seoScore}/100. Meta tags, heading structure, and mobile-friendliness evaluated.`,
      mobile_performance_notes: `Mobile performance optimized for ${lhr?.configSettings?.emulatedFormFactor || 'mobile'} devices.`,
      optimization_priorities: metrics.recommendations.slice(0, 5).map(rec => rec.title),
      business_impact_assessment: metrics.conversionImpact,
      technical_debt_analysis: 'Performance debt identified in resource loading and rendering optimization.',
      monitoring_recommendations: [
        'Monitor Core Web Vitals monthly',
        'Set up performance budgets',
        'Implement real user monitoring',
        'Track conversion impact of performance changes'
      ]
    };

    await supabaseAdmin
      .from('advanced_metrics_summary')
      .upsert(advancedSummary);

    log.info(`Saved advanced metrics summary for analysis ${analysisId}`);
  } catch (error) {
    log.error(`Error saving advanced metrics summary: ${error}`);
  }
}

/**
 * Load lighthouse analysis results from database
 */
export async function loadLighthouseDataFromDatabase(analysisId: string): Promise<LighthouseMetrics | null> {
  try {
    log.info(`Loading lighthouse data from database for analysis ${analysisId}`);

    // Get the most recent audit run for this analysis
    const { data: auditRun, error: auditError } = await supabaseAdmin
      .from('lighthouse_audit_runs')
      .select(`
        *,
        lighthouse_performance_metrics(*),
        lighthouse_accessibility_metrics(*),
        lighthouse_seo_metrics(*),
        lighthouse_best_practices_metrics(*)
      `)
      .eq('analysis_id', analysisId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (auditError || !auditRun) {
      log.info(`No lighthouse data found in database for analysis ${analysisId}`);
      return null;
    }

    const perfMetrics = auditRun.lighthouse_performance_metrics[0];
    const accessMetrics = auditRun.lighthouse_accessibility_metrics[0];
    const seoMetrics = auditRun.lighthouse_seo_metrics[0];
    const bpMetrics = auditRun.lighthouse_best_practices_metrics[0];

    if (!perfMetrics) {
      log.warn(`Performance metrics not found for analysis ${analysisId}`);
      return null;
    }

    // Reconstruct the LighthouseMetrics object
    const performanceScore = perfMetrics.performance_score || 0;
    const accessibilityScore = accessMetrics?.accessibility_score || 0;
    const seoScore = seoMetrics?.seo_score || 0;
    const bestPracticesScore = bpMetrics?.best_practices_score || 0;

    const grade = calculateGrade(performanceScore);
    const conversionImpact = getConversionImpactMessage(performanceScore);

    // Generate recommendations based on stored data
    const recommendations = [];

    if (performanceScore < 80) {
      recommendations.push({
        title: 'Optimize Page Load Speed',
        description: 'Improve server response times and optimize resource loading',
        impact: 'High' as const,
        effort: 'Medium' as const,
        category: 'Performance'
      });
    }

    const lcpScore = (perfMetrics.largest_contentful_paint_ms || 0) / 1000;
    const fidScore = perfMetrics.first_input_delay_ms || 0;
    const clsScore = perfMetrics.cumulative_layout_shift || 0;

    if (lcpScore > 2.5) {
      recommendations.push({
        title: 'Improve Largest Contentful Paint',
        description: 'Optimize images and critical resources to load faster',
        impact: 'High' as const,
        effort: 'Medium' as const,
        category: 'Core Web Vitals'
      });
    }

    if (fidScore > 100) {
      recommendations.push({
        title: 'Reduce Input Delay',
        description: 'Minimize JavaScript execution time and optimize event handlers',
        impact: 'Medium' as const,
        effort: 'High' as const,
        category: 'Core Web Vitals'
      });
    }

    if (clsScore > 0.1) {
      recommendations.push({
        title: 'Reduce Layout Shift',
        description: 'Set explicit dimensions for images and ads to prevent layout shifts',
        impact: 'Medium' as const,
        effort: 'Low' as const,
        category: 'Core Web Vitals'
      });
    }

    const metrics: LighthouseMetrics = {
      performanceScore,
      accessibilityScore,
      bestPracticesScore,
      seoScore,
      coreWebVitals: {
        lcp: Math.round(lcpScore * 100) / 100,
        fid: Math.round(fidScore),
        cls: Math.round(clsScore * 1000) / 1000
      },
      grade,
      conversionImpact,
      recommendations
    };

    log.info(`Successfully loaded lighthouse data from database for analysis ${analysisId}`);
    return metrics;

  } catch (error) {
    log.error(`Error loading lighthouse data from database: ${error}`);
    return null;
  }
}

export async function runLighthouseAnalysis(url: string, analysisId?: string): Promise<LighthouseMetrics> {
  try {
    log.info(`Starting Lighthouse analysis for ${url}`);

    // Dynamic import to avoid build issues
    const puppeteer = await import('puppeteer');
    const lighthouse = await import('lighthouse');

    // Launch browser with optimized settings
    const browser = await puppeteer.default.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    try {
      // Run Lighthouse audit
      const lighthouseResult = await lighthouse.default(url, {
        port: parseInt(new URL(browser.wsEndpoint()).port, 10),
        output: 'json',
        logLevel: 'error',
        onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      }, {
        extends: 'lighthouse:default',
        settings: {
          maxWaitForFcp: 15 * 1000,
          maxWaitForLoad: 35 * 1000,
          formFactor: 'mobile', // Fixed: consistent with mobile analysis requirements
          throttling: {
            rttMs: 40,
            throughputKbps: 10240,
            cpuSlowdownMultiplier: 1
          }
        }
      });

      if (!lighthouseResult) {
        throw new Error('Lighthouse analysis returned no result.');
      }

      const { lhr } = lighthouseResult;

      // Debug logging to understand what's happening with scores
      log.info('Lighthouse categories available:', Object.keys(lhr.categories || {}));
      log.info('Performance category score:', lhr.categories.performance?.score);
      log.info('SEO category score:', lhr.categories.seo?.score);

      // Log the full performance category to debug
      if (lhr.categories.performance) {
        log.info('Performance category details:', {
          score: lhr.categories.performance.score,
          title: lhr.categories.performance.title,
          auditRefs: lhr.categories.performance.auditRefs?.length || 0
        });
      } else {
        log.error('Performance category is missing from Lighthouse results!');
      }

      // Extract scores with better error handling
      const performanceScore = lhr.categories.performance?.score ? Math.round(lhr.categories.performance.score * 100) : 0;
      const accessibilityScore = lhr.categories.accessibility?.score ? Math.round(lhr.categories.accessibility.score * 100) : 0;
      const bestPracticesScore = lhr.categories['best-practices']?.score ? Math.round(lhr.categories['best-practices'].score * 100) : 0;
      const seoScore = lhr.categories.seo?.score ? Math.round(lhr.categories.seo.score * 100) : 0;

      log.info('Calculated scores:', { performanceScore, accessibilityScore, bestPracticesScore, seoScore });

      // Extract Core Web Vitals with proper rounding to avoid decimal precision issues
      const lcpScore = Math.round(((lhr.audits['largest-contentful-paint']?.numericValue || 0) / 1000) * 100) / 100;
      const fidScore = Math.round(lhr.audits['max-potential-fid']?.numericValue || 0);
      const clsScore = Math.round((lhr.audits['cumulative-layout-shift']?.numericValue || 0) * 1000) / 1000;

      const grade = calculateGrade(performanceScore);
      const conversionImpact = getConversionImpactMessage(performanceScore);

      // Generate performance recommendations
      const recommendations = [];
      
      if (performanceScore < 80) {
        recommendations.push({
          title: 'Optimize Page Load Speed',
          description: 'Improve server response times and optimize resource loading',
          impact: 'High' as const,
          effort: 'Medium' as const,
          category: 'Performance'
        });
      }

      if (lcpScore > 2.5) {
        recommendations.push({
          title: 'Improve Largest Contentful Paint',
          description: 'Optimize images and critical rendering path to reduce LCP',
          impact: 'High' as const,
          effort: 'Medium' as const,
          category: 'Core Web Vitals'
        });
      }

      if (clsScore > 0.1) {
        recommendations.push({
          title: 'Reduce Layout Shift',
          description: 'Set explicit dimensions for images and ads to prevent layout shifts',
          impact: 'Medium' as const,
          effort: 'Low' as const,
          category: 'Core Web Vitals'
        });
      }

      const metrics: LighthouseMetrics = {
        performanceScore,
        accessibilityScore,
        bestPracticesScore,
        seoScore,
        coreWebVitals: {
          lcp: Math.round(lcpScore * 100) / 100,
          fid: Math.round(fidScore),
          cls: Math.round(clsScore * 1000) / 1000
        },
        grade,
        conversionImpact,
        recommendations
      };

      // Save to database if analysisId is provided
      if (analysisId) {
        try {
          await saveLighthouseDataToDatabase(analysisId, lighthouseResult, metrics);
          log.info(`Lighthouse data saved to database for analysis ${analysisId}`);
        } catch (dbError) {
          log.warn(`Failed to save lighthouse data to database: ${dbError}`);
          // Don't throw error - continue with analysis
        }
      }

      await browser.close();

      log.info(`Lighthouse analysis completed for ${url}. Performance: ${performanceScore}`);

      return metrics;

    } finally {
      await browser.close();
    }

  } catch (error) {
    log.error(`Lighthouse analysis failed: ${error instanceof Error ? error.message : error}`);
    throw new Error(`Lighthouse analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function runSEOAnalysis(url: string): Promise<SEOAnalysisResult> {
  try {
    log.info(`Starting SEO analysis for ${url}`);

    const puppeteer = await import('puppeteer');
    const browser = await puppeteer.default.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Extract SEO data
      const seoData = await page.evaluate(() => {
        const title = document.querySelector('title')?.textContent?.trim() || '';
        const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content')?.trim() || '';
        const canonical = document.querySelector('link[rel="canonical"]')?.getAttribute('href') || '';

        // Open Graph tags
        const ogTags: Record<string, string> = {};
        document.querySelectorAll('meta[property^="og:"]').forEach(tag => {
          const property = tag.getAttribute('property');
          const content = tag.getAttribute('content');
          if (property && content) {
            ogTags[property] = content;
          }
        });

        // Heading structure
        const headings: Array<{level: number, text: string}> = [];
        document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
          headings.push({
            level: parseInt(heading.tagName.charAt(1)),
            text: heading.textContent?.trim() || ''
          });
        });

        // Images analysis
        const imagesWithoutAlt = Array.from(document.querySelectorAll('img')).filter(img => !img.getAttribute('alt')).length;
        const totalImages = document.querySelectorAll('img').length;

        // Structured data
        const structuredData: any[] = [];
        document.querySelectorAll('script[type="application/ld+json"]').forEach(script => {
          try {
            const data = JSON.parse(script.textContent || '');
            structuredData.push(data);
          } catch (e) {
            // Invalid JSON-LD
          }
        });

        return {
          title,
          metaDescription,
          canonical,
          ogTags,
          headings,
          structuredData,
          bodyText: document.body.textContent || '',
          imagesWithoutAlt,
          totalImages,
          hasH1: headings.some(h => h.level === 1),
          h1Count: headings.filter(h => h.level === 1).length
        };
      });

      await browser.close();

      // Generate SEO issues
      const issues = [];
      const pros = [];
      const cons = [];

      // Title analysis
      if (!seoData.title) {
        issues.push({
          issue: 'Missing Title Tag',
          recommendation: 'Add a descriptive title tag (50-60 characters)',
          severity_score: 5
        });
        cons.push('Missing title tag');
      } else {
        if (seoData.title.length >= 30 && seoData.title.length <= 60) {
          pros.push('Well-optimized title tag length');
        } else if (seoData.title.length < 30) {
          issues.push({
            issue: 'Title Tag Too Short',
            recommendation: `Expand title to 50-60 characters (currently ${seoData.title.length})`,
            severity_score: 3
          });
          cons.push('Title tag too short');
        } else {
          issues.push({
            issue: 'Title Tag Too Long',
            recommendation: `Shorten title to under 60 characters (currently ${seoData.title.length})`,
            severity_score: 3
          });
          cons.push('Title tag too long');
        }
      }

      // Meta description analysis
      if (!seoData.metaDescription) {
        issues.push({
          issue: 'Missing Meta Description',
          recommendation: 'Add a compelling meta description (150-160 characters)',
          severity_score: 3
        });
        cons.push('Missing meta description');
      } else {
        if (seoData.metaDescription.length <= 160) {
          pros.push('Good meta description length');
        } else {
          issues.push({
            issue: 'Meta Description Too Long',
            recommendation: `Shorten meta description to under 160 characters (currently ${seoData.metaDescription.length})`,
            severity_score: 2
          });
          cons.push('Meta description too long');
        }
      }

      // H1 analysis
      if (!seoData.hasH1) {
        issues.push({
          issue: 'Missing H1 Tag',
          recommendation: 'Add a single, descriptive H1 tag to the page',
          severity_score: 4
        });
        cons.push('Missing H1 tag');
      } else if (seoData.h1Count === 1) {
        pros.push('Proper H1 tag usage');
      } else {
        issues.push({
          issue: 'Multiple H1 Tags',
          recommendation: `Use only one H1 tag per page (currently ${seoData.h1Count})`,
          severity_score: 3
        });
        cons.push('Multiple H1 tags');
      }

      // Images analysis
      if (seoData.imagesWithoutAlt > 0) {
        issues.push({
          issue: 'Images Missing Alt Text',
          recommendation: `Add alt text to ${seoData.imagesWithoutAlt} images for accessibility and SEO`,
          severity_score: 3
        });
        cons.push('Images missing alt text');
      } else if (seoData.totalImages > 0) {
        pros.push('All images have alt text');
      }

      // Structured data analysis
      if (seoData.structuredData.length > 0) {
        pros.push('Structured data implementation');
      } else {
        issues.push({
          issue: 'No Structured Data',
          recommendation: 'Add JSON-LD structured data for better search visibility',
          severity_score: 2
        });
        cons.push('No structured data');
      }

      // Calculate SEO score
      let score = 100;
      issues.forEach(issue => {
        if (issue.severity_score >= 5) score -= 20;
        else if (issue.severity_score >= 4) score -= 15;
        else if (issue.severity_score >= 3) score -= 10;
        else score -= 5;
      });

      log.info(`SEO analysis completed for ${url}. Score: ${Math.max(0, score)}`);

      return {
        score: Math.max(0, score),
        issues,
        data: {
          title: seoData.title,
          metaDescription: seoData.metaDescription,
          canonicalUrl: seoData.canonical,
          openGraphTags: seoData.ogTags,
          structuredData: seoData.structuredData,
          headingStructure: seoData.headings,
          keywordDensity: {}
        },
        pros,
        cons
      };

    } finally {
      await browser.close();
    }

  } catch (error) {
    log.error(`SEO analysis failed: ${error instanceof Error ? error.message : error}`);
    throw new Error(`SEO analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function captureScreenshot(url: string): Promise<Buffer> {
  let browser;
  try {
    log.info(`[Screenshot] Capturing for ${url}`);
    const puppeteer = await import('puppeteer');

    log.info('[Screenshot] Launching browser...');
    browser = await puppeteer.default.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
    });

    log.info('[Screenshot] Browser launched. Opening new page...');
    const page = await browser.newPage();

    const viewport = { width: 1280, height: 800 };
    log.info(`[Screenshot] Setting viewport to ${viewport.width}x${viewport.height}`);
    await page.setViewport(viewport);

    log.info(`[Screenshot] Navigating to ${url}...`);
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 45000 });
    log.info('[Screenshot] Page loaded.');

    const screenshotOptions = {
      type: 'png' as const,
      fullPage: false,
    };
    log.info(`[Screenshot] Taking screenshot with options: ${JSON.stringify(screenshotOptions)}`);
    const screenshot = await page.screenshot(screenshotOptions);

    log.info(`[Screenshot] Capture successful for ${url}`);
    return screenshot as Buffer;

  } catch (error) {
    log.error(`[Screenshot] Capture failed for ${url}: ${error instanceof Error ? error.message : error}`);
    throw new Error(`[Screenshot] Capture failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (browser) {
      log.info('[Screenshot] Closing browser.');
      await browser.close();
    }
  }
}