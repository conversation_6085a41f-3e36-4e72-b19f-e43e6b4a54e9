<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import { generateAIPageSummary } from '../../lib/ai-contextual-analyzer';
import type { Database } from '../../types/supabase';
import { FileText, Users, TrendingUp, BarChart3, Target, Loader2, CheckCircle, AlertTriangle } from 'lucide-vue-next';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const aiPageSummary = ref<{
  pagePurpose: string;
  keyTakeaways: string[];
  heroEffectiveness: string;
  valuePropositionClarity: string;
  leadCaptureAssessment: string;
  userJourneyAnalysis: string;
  businessImpactSummary: string;
} | null>(null);

const isLoadingAI = ref(false);
const aiError = ref<string | null>(null);

onMounted(async () => {
  await loadAIPageSummary();
});

async function loadAIPageSummary() {
  if (!props.analysis) return;

  isLoadingAI.value = true;
  aiError.value = null;

  try {
    // Check if AI page summary already exists in database with enhanced data
    const { data: existingSummary } = await supabase
      .from('ai_page_summaries')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .single();

    if (existingSummary) {
      // Use existing summary with enhanced data
      aiPageSummary.value = {
        pagePurpose: existingSummary.page_purpose,
        keyTakeaways: existingSummary.key_takeaways || [],
        heroEffectiveness: existingSummary.hero_effectiveness || '',
        valuePropositionClarity: existingSummary.value_proposition_clarity || '',
        leadCaptureAssessment: existingSummary.lead_capture_assessment || '',
        userJourneyAnalysis: existingSummary.user_journey_analysis || '',
        businessImpactSummary: existingSummary.business_impact_summary || '',
      };
    } else {
      // No summary found - content may still be generating
      aiError.value = 'Page summary is being generated. Please wait for the analysis to complete.';
      aiPageSummary.value = null;
    }
  } catch (error) {
    console.error('Error loading AI page summary:', error);
    aiError.value = 'Failed to load page summary. Please refresh the page or try again later.';
    aiPageSummary.value = null;
  } finally {
    isLoadingAI.value = false;
  }
}

// Calculate overall metrics with proper 0-10 scaling
const overallMetrics = computed(() => {
  const conversionScore = props.analysis.score || 0; // Already 0-10
  const performanceScore = props.analysis.performance_score || 0; // 0-100
  const seoScore = props.analysis.seo_score || 0; // 0-100

  // Calculate overall rating using weighted average (conversion 50%, performance 30%, SEO 20%)
  const overallRating = Math.round(
    (conversionScore * 0.5) +
    ((performanceScore / 10) * 0.3) +
    ((seoScore / 10) * 0.2) * 10
  ) / 10;

  return {
    conversionScore: Math.round(conversionScore * 10) / 10, // Ensure 1 decimal place
    performanceScore,
    seoScore,
    overallRating: Math.max(0, Math.min(10, overallRating)), // Clamp to 0-10
    totalSuggestions: props.analysis.suggestions_count || 0,
    priorityIssues: props.analysis.priority_issues_count || 0
  };
});

const getScoreColor = (score: number) => {
  if (score >= 8) return 'text-green-600';
  if (score >= 6) return 'text-blue-600';
  if (score >= 4) return 'text-yellow-600';
  if (score >= 2) return 'text-orange-600';
  return 'text-red-600';
};

const getScoreBg = (score: number) => {
  if (score >= 8) return 'bg-green-50 border-green-200';
  if (score >= 6) return 'bg-blue-50 border-blue-200';
  if (score >= 4) return 'bg-yellow-50 border-yellow-200';
  if (score >= 2) return 'bg-orange-50 border-orange-200';
  return 'bg-red-50 border-red-200';
};

const getScoreLabel = (score: number) => {
  if (score >= 8) return 'Excellent';
  if (score >= 6) return 'Good';
  if (score >= 4) return 'Fair';
  if (score >= 2) return 'Poor';
  return 'Critical';
};
</script>

<template>
  <div class="space-y-8">
    <!-- AI Page Summary Section -->
    <div class="bg-white rounded-lg">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <FileText class="w-5 h-5 text-gray-400 mr-2" />
          <h3 class="text-lg font-semibold text-gray-900">AI-Powered Page Summary</h3>
          
          <!-- AI Status Indicators -->
          <div v-if="isLoadingAI" class="ml-3 flex items-center">
            <Loader2 class="w-4 h-4 animate-spin text-blue-600 mr-1" />
            <span class="text-xs text-blue-600">Generating AI summary...</span>
          </div>
          <div v-else-if="aiError" class="ml-3 flex items-center">
            <AlertTriangle class="w-4 h-4 text-yellow-500 mr-1" />
            <span class="text-xs text-yellow-600">Using fallback summary</span>
          </div>
          <div v-else-if="aiPageSummary" class="ml-3 flex items-center">
            <CheckCircle class="w-4 h-4 text-green-500 mr-1" />
            <span class="text-xs text-green-600">AI-powered summary</span>
          </div>
        </div>
      </div>
      
      <!-- Loading State -->
      <div v-if="isLoadingAI" class="text-center py-12">
        <Loader2 class="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
        <p class="text-gray-600">Analyzing page content to generate comprehensive summary...</p>
      </div>

      <!-- AI Summary Content -->
      <div v-else-if="aiPageSummary">
        <!-- Page Purpose -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6 border border-blue-200">
          <h4 class="font-medium text-blue-900 mb-3 flex items-center">
            <Target class="w-5 h-5 mr-2" />
            AI Analysis: Page Purpose
          </h4>
          <p class="text-blue-800 leading-relaxed">{{ aiPageSummary.pagePurpose }}</p>
        </div>

        <!-- Key Metrics Overview -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <!-- Overall Rating (0-10) -->
          <div class="text-center p-4 rounded-lg border" :class="getScoreBg(overallMetrics.overallRating)">
            <div class="text-2xl font-bold" :class="getScoreColor(overallMetrics.overallRating)">
              {{ overallMetrics.overallRating }}/10
            </div>
            <div class="text-sm text-gray-600 mt-1">Overall</div>
            <div class="text-xs font-medium mt-1" :class="getScoreColor(overallMetrics.overallRating)">
              {{ getScoreLabel(overallMetrics.overallRating) }}
            </div>
          </div>

          <!-- Conversion Score (0-10) -->
          <div class="text-center p-4 rounded-lg border" :class="getScoreBg(overallMetrics.conversionScore)">
            <div class="text-2xl font-bold" :class="getScoreColor(overallMetrics.conversionScore)">
              {{ overallMetrics.conversionScore }}/10
            </div>
            <div class="text-sm text-gray-600 mt-1">Conversion</div>
            <div class="text-xs font-medium mt-1" :class="getScoreColor(overallMetrics.conversionScore)">
              {{ getScoreLabel(overallMetrics.conversionScore) }}
            </div>
          </div>

          <!-- Performance Score (0-100) -->
          <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div class="text-2xl font-bold text-blue-600">{{ overallMetrics.performanceScore || 'N/A' }}</div>
            <div class="text-sm text-gray-600 mt-1">Performance</div>
            <div class="text-xs text-blue-600 mt-1">0-100 scale</div>
          </div>

          <!-- SEO Score (0-100) -->
          <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div class="text-2xl font-bold text-green-600">{{ overallMetrics.seoScore || 'N/A' }}</div>
            <div class="text-sm text-gray-600 mt-1">SEO</div>
            <div class="text-xs text-green-600 mt-1">0-100 scale</div>
          </div>
        </div>

        <!-- AI Analysis Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Hero Section Analysis -->
          <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <h4 class="font-medium text-purple-900 mb-3">Hero Section Effectiveness</h4>
            <p class="text-sm text-purple-800">{{ aiPageSummary.heroEffectiveness }}</p>
          </div>

          <!-- Value Proposition Analysis -->
          <div class="bg-green-50 rounded-lg p-4 border border-green-200">
            <h4 class="font-medium text-green-900 mb-3">Value Proposition Clarity</h4>
            <p class="text-sm text-green-800">{{ aiPageSummary.valuePropositionClarity }}</p>
          </div>

          <!-- Lead Capture Analysis -->
          <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
            <h4 class="font-medium text-orange-900 mb-3">Lead Capture Assessment</h4>
            <p class="text-sm text-orange-800">{{ aiPageSummary.leadCaptureAssessment }}</p>
          </div>

          <!-- User Journey Analysis -->
          <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 class="font-medium text-blue-900 mb-3">User Journey Analysis</h4>
            <p class="text-sm text-blue-800">{{ aiPageSummary.userJourneyAnalysis }}</p>
          </div>
        </div>

        <!-- Key Takeaways -->
        <div class="bg-white rounded-lg border border-gray-200 p-6 mt-6">
          <div class="flex items-center mb-4">
            <Users class="w-5 h-5 text-gray-400 mr-2" />
            <h4 class="text-lg font-semibold text-gray-900">What Leads Take Away From This Page</h4>
          </div>
          
          <div class="space-y-3">
            <div 
              v-for="(takeaway, index) in aiPageSummary.keyTakeaways" 
              :key="index"
              class="flex items-start p-3 bg-green-50 rounded-lg border border-green-200"
            >
              <CheckCircle class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span class="text-sm text-green-800">{{ takeaway }}</span>
            </div>
          </div>
        </div>

        <!-- Business Impact Summary -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200 p-6 mt-6">
          <h4 class="font-medium text-green-900 mb-3 flex items-center">
            <TrendingUp class="w-4 h-4 mr-2" />
            AI Business Impact Summary
          </h4>
          <p class="text-sm text-green-800">{{ aiPageSummary.businessImpactSummary }}</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="aiError" class="text-center py-12">
        <AlertTriangle class="w-8 h-8 text-yellow-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">AI Summary Unavailable</h3>
        <p class="text-gray-600 mb-4">{{ aiError }}</p>
        <button 
          @click="loadAIPageSummary" 
          class="btn-primary"
        >
          Retry AI Summary
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}
</style>