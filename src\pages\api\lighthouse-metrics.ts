import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { loadLighthouseDataFromDatabase } from '../../lib/lighthouse-analyzer';
import { log } from '../../lib/logger';

export const prerender = false;

export const GET: APIRoute = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const analysisId = url.searchParams.get('analysisId');

    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'Missing analysisId parameter' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    log.info(`Loading lighthouse metrics for analysis ${analysisId}`);

    // Load lighthouse data from database tables
    const lighthouseMetrics = await loadLighthouseDataFromDatabase(analysisId);

    if (!lighthouseMetrics) {
      return new Response(JSON.stringify({ 
        error: 'No lighthouse metrics found for this analysis',
        hasData: false 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Also get detailed metrics from individual tables
    const { data: auditRun, error: auditError } = await supabaseAdmin
      .from('lighthouse_audit_runs')
      .select(`
        *,
        lighthouse_performance_metrics(*),
        lighthouse_accessibility_metrics(*),
        lighthouse_seo_metrics(*),
        lighthouse_best_practices_metrics(*)
      `)
      .eq('analysis_id', analysisId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (auditError) {
      log.warn(`Could not load detailed audit data: ${auditError.message}`);
    }

    const response = {
      hasData: true,
      metrics: lighthouseMetrics,
      detailedMetrics: auditRun ? {
        auditRun: {
          id: auditRun.id,
          audit_timestamp: auditRun.audit_timestamp,
          lighthouse_version: auditRun.lighthouse_version,
          device_type: auditRun.device_type
        },
        performance: auditRun.lighthouse_performance_metrics?.[0] || null,
        accessibility: auditRun.lighthouse_accessibility_metrics?.[0] || null,
        seo: auditRun.lighthouse_seo_metrics?.[0] || null,
        bestPractices: auditRun.lighthouse_best_practices_metrics?.[0] || null
      } : null,
      lastUpdated: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    log.error(`Error loading lighthouse metrics: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to load lighthouse metrics',
      details: error instanceof Error ? error.message : 'Unknown error',
      hasData: false
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
