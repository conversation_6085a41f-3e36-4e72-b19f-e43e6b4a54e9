import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';

export const prerender = false;

const OPENROUTER_API_KEY = import.meta.env.PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { suggestions } = await request.json();

    if (!suggestions || !Array.isArray(suggestions)) {
      return new Response(JSON.stringify({ error: 'Invalid suggestions data' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const userPrompt = `You are a UX/conversion optimization expert. Generate concise, descriptive headers for these website improvement suggestions. Each header should be 3-6 words and clearly convey the main benefit or action.

SUGGESTIONS:
${suggestions.map((s, i) => `${i + 1}. Category: ${s.category}
Title: ${s.title}
Description: ${s.description.substring(0, 200)}...`).join('\n\n')}

Return a JSON array with exactly ${suggestions.length} headers in the same order. Each header should be:
- 3-6 words maximum
- Action-oriented when possible
- Clear and specific
- Professional tone

Example format:
["Optimize Page Loading Speed", "Improve Mobile Navigation", "Enhance Call-to-Action"]`;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'qwen/qwen-2.5-72b-instruct:free',
        messages: [
          {
            role: "system",
            content: "You are a UX expert who creates clear, actionable headers for website improvement suggestions."
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        response_format: { type: "json_object" },
        max_tokens: 500,
      })
    });

    const data = await response.json();
    if (data.error) throw new Error(data.error.message);
    
    const content = data.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No response from OpenRouter');
    }

    let headers: string[];
    try {
      const parsed = JSON.parse(content);
      headers = parsed.headers || parsed; // Handle both formats
    } catch (parseError) {
      console.error('Failed to parse OpenRouter response:', content);
      // Fallback: generate simple headers from titles
      headers = suggestions.map(s => {
        const words = s.title.split(' ').slice(0, 4);
        return words.join(' ');
      });
    }

    // Ensure we have the right number of headers
    if (headers.length !== suggestions.length) {
      console.warn('Header count mismatch, using fallback');
      headers = suggestions.map(s => {
        const words = s.title.split(' ').slice(0, 4);
        return words.join(' ');
      });
    }

    // Save headers to database
    const headerInserts = suggestions.map((suggestion, index) => ({
      suggestion_id: suggestion.id,
      ai_generated_title: headers[index],
      original_title: suggestion.title
    }));

    const { error: insertError } = await supabaseAdmin
      .from('suggestion_headers')
      .upsert(headerInserts, {
        onConflict: 'suggestion_id'
      });

    if (insertError) {
      console.error('Error saving suggestion headers:', insertError);
      throw insertError;
    }

    return new Response(JSON.stringify({
      success: true,
      headers: headers,
      message: `Generated ${headers.length} suggestion headers`
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error generating suggestion headers:', error);
    return new Response(JSON.stringify({
      error: 'Failed to generate suggestion headers',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};