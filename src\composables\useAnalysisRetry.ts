import { ref } from 'vue';
import { supabase } from '../lib/supabase';

export interface RetryState {
  isRetrying: boolean;
  retryError: string | null;
  lastRetryTime: Date | null;
}

export interface AnalysisStep {
  stepNumber: number;
  stepName: string;
  status: 'completed' | 'failed' | 'in_progress' | 'pending';
  errorMessage?: string;
  retryCount?: number;
}

export function useAnalysisRetry() {
  const retryStates = ref<Record<number, RetryState>>({});
  const analysisSteps = ref<AnalysisStep[]>([]);

  /**
   * Get the current retry state for a specific step
   */
  const getRetryState = (stepNumber: number): RetryState => {
    if (!retryStates.value[stepNumber]) {
      retryStates.value[stepNumber] = {
        isRetrying: false,
        retryError: null,
        lastRetryTime: null
      };
    }
    return retryStates.value[stepNumber];
  };

  /**
   * Load analysis generation progress from database
   */
  const loadAnalysisProgress = async (analysisId: string): Promise<AnalysisStep[]> => {
    try {
      const { data, error } = await supabase
        .from('analysis_generation_progress')
        .select('*')
        .eq('analysis_id', analysisId)
        .order('step_number', { ascending: true });

      if (error) {
        console.error('Error loading analysis progress:', error);
        return [];
      }

      const steps: AnalysisStep[] = (data || []).map(row => ({
        stepNumber: row.step_number,
        stepName: row.current_step,
        status: row.status,
        errorMessage: row.error_message,
        retryCount: row.retry_count || 0
      }));

      analysisSteps.value = steps;
      return steps;
    } catch (error) {
      console.error('Error loading analysis progress:', error);
      return [];
    }
  };

  /**
   * Retry a specific analysis step
   */
  const retryAnalysisStep = async (analysisId: string, stepNumber: number): Promise<boolean> => {
    const retryState = getRetryState(stepNumber);
    
    try {
      retryState.isRetrying = true;
      retryState.retryError = null;

      const response = await fetch('/api/retry-analysis-step', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          analysisId,
          stepNumber
        })
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || 'Failed to retry analysis step');
      }

      const result = await response.json();
      
      retryState.lastRetryTime = new Date();
      
      // Refresh the analysis progress
      await loadAnalysisProgress(analysisId);
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      retryState.retryError = errorMessage;
      console.error(`Error retrying step ${stepNumber}:`, error);
      return false;
    } finally {
      retryState.isRetrying = false;
    }
  };

  /**
   * Check if a step can be retried
   */
  const canRetryStep = (stepNumber: number): boolean => {
    const step = analysisSteps.value.find(s => s.stepNumber === stepNumber);
    return step?.status === 'failed';
  };

  /**
   * Get failed steps that can be retried
   */
  const getFailedSteps = (): AnalysisStep[] => {
    return analysisSteps.value.filter(step => step.status === 'failed');
  };

  /**
   * Get step display name for UI
   */
  const getStepDisplayName = (stepNumber: number): string => {
    const stepNames: Record<number, string> = {
      1: 'Website Context Extraction',
      2: 'Pros & Cons Analysis',
      3: 'Performance Impact Analysis', 
      4: 'Lead Insights Generation',
      5: 'Page Summary Creation',
      6: 'SEO Analysis',
      7: 'Business Impact Forecast',
      8: 'Analysis Finalization'
    };
    return stepNames[stepNumber] || `Step ${stepNumber}`;
  };

  /**
   * Get step tab mapping for navigation
   */
  const getStepTabMapping = (stepNumber: number): string | null => {
    const tabMapping: Record<number, string> = {
      2: 'pros-cons',
      3: 'performance',
      4: 'lead-insights',
      5: 'summary'
    };
    return tabMapping[stepNumber] || null;
  };

  /**
   * Retry all failed steps
   */
  const retryAllFailedSteps = async (analysisId: string): Promise<{ success: number; failed: number }> => {
    const failedSteps = getFailedSteps();
    let successCount = 0;
    let failedCount = 0;

    for (const step of failedSteps) {
      const success = await retryAnalysisStep(analysisId, step.stepNumber);
      if (success) {
        successCount++;
      } else {
        failedCount++;
      }
      
      // Add a small delay between retries to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return { success: successCount, failed: failedCount };
  };

  /**
   * Subscribe to real-time progress updates
   */
  const subscribeToProgressUpdates = (analysisId: string, callback?: (step: AnalysisStep) => void) => {
    const subscription = supabase
      .channel(`analysis_progress_${analysisId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'analysis_generation_progress',
          filter: `analysis_id=eq.${analysisId}`
        },
        (payload) => {
          console.log('Progress update received:', payload);
          
          if (payload.new) {
            const updatedStep: AnalysisStep = {
              stepNumber: payload.new.step_number,
              stepName: payload.new.current_step,
              status: payload.new.status,
              errorMessage: payload.new.error_message,
              retryCount: payload.new.retry_count || 0
            };

            // Update the local state
            const stepIndex = analysisSteps.value.findIndex(s => s.stepNumber === updatedStep.stepNumber);
            if (stepIndex >= 0) {
              analysisSteps.value[stepIndex] = updatedStep;
            } else {
              analysisSteps.value.push(updatedStep);
            }

            // Call the callback if provided
            if (callback) {
              callback(updatedStep);
            }
          }
        }
      )
      .subscribe();

    return subscription;
  };

  return {
    retryStates,
    analysisSteps,
    getRetryState,
    loadAnalysisProgress,
    retryAnalysisStep,
    canRetryStep,
    getFailedSteps,
    getStepDisplayName,
    getStepTabMapping,
    retryAllFailedSteps,
    subscribeToProgressUpdates
  };
}
