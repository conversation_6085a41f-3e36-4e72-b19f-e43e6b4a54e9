<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { supabase } from '../../lib/supabase';
import { generateAIPerformanceInsights } from '../../lib/ai-contextual-analyzer';
import type { Database } from '../../types/supabase';
import { CORE_WEB_VITALS_THRESHOLDS, evaluateMetric } from '../../lib/performance-analyzer';
import { Info, HelpCircle, TrendingUp, RefreshCw, Gauge, CheckCircle, AlertTriangle, Loader2, ChevronLeft, ChevronRight } from 'lucide-vue-next';
import PerformanceBreakdownChart from '../charts/PerformanceBreakdownChart.vue';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const emit = defineEmits<{
  'refresh-analysis': [];
}>();

const aiPerformanceInsights = ref<{
  primaryIssue: {
    title: string;
    description: string;
    evidence: string;
    conversionImpact: string;
  };
  keyFindings: Array<{
    metric: string;
    currentValue: string;
    targetValue: string;
    impact: string;
  }>;
  recommendations: Array<{
    title: string;
    description: string;
    expectedImprovement: string;
    priority: string;
  }>;
  businessImpact: string;
} | null>(null);

const isLoadingAI = ref(false);
const aiError = ref<string | null>(null);
const showAdvancedMetrics = ref(false);
const showTooltip = ref<string | null>(null);
const advancedSummary = ref<any>(null);
const isLoadingAdvancedSummary = ref(false);
const advancedSummaryError = ref<string | null>(null);
const isReloading = ref(false);

// --- Refs for carousel ---
const recommendationsContainer = ref<HTMLElement | null>(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
const isDragging = ref(false);
const startX = ref(0);
const scrollLeft = ref(0);


// Performance chart data from database
const performanceChartData = ref<any>(null);
const lighthouseMetricsData = ref<any>(null);

// Load performance chart data from database
async function loadPerformanceChartData() {
  try {
    const { data, error } = await supabase
      .from('performance_chart_data')
      .select('*')
      .eq('analysis_id', props.analysis.id);

    if (error) throw error;

    if (data && data.length > 0) {
      // Convert array to object with chart_type as key
      const chartDataMap: any = {};
      data.forEach(item => {
        chartDataMap[item.chart_type] = {
          data: item.chart_data,
          config: item.chart_config
        };
      });
      performanceChartData.value = chartDataMap;
    }
  } catch (error) {
    console.error('Error loading performance chart data:', error);
  }
}

// Load lighthouse metrics from database
async function loadLighthouseMetrics() {
  try {
    const response = await fetch(`/api/lighthouse-metrics?analysisId=${props.analysis.id}`);
    const data = await response.json();

    if (data.hasData) {
      lighthouseMetricsData.value = data;
      console.log('Loaded lighthouse metrics:', data);
    } else {
      console.warn('No lighthouse metrics found for analysis:', props.analysis.id);
    }
  } catch (error) {
    console.error('Error loading lighthouse metrics:', error);
  }
}

// Load advanced metrics summary
async function loadAdvancedMetrics() {
  try {
    const { data, error } = await supabase
      .from('advanced_metrics_summary')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .single();

    if (error) {
      console.warn('No advanced metrics found:', error);
      return;
    }

    if (data) {
      // Update the enhanced performance data with advanced metrics
      const advancedData = data.performance_breakdown || {};
      console.log('Loaded advanced metrics:', advancedData);

      // You can use this data to populate the advanced metrics display
      // The data structure includes timingMetrics, networkMetrics, and advancedMetrics
    }
  } catch (error) {
    console.error('Error loading advanced metrics:', error);
  }
}

// Enhanced performance data that includes chart data from database
const enhancedPerformanceData = computed(() => {
  const baseData = {
    lcp: props.analysis.lcp_score || 2.5,
    fid: props.analysis.fid_score || 100,
    cls: props.analysis.cls_score || 0.1,
    performanceScore: props.analysis.performance_score || 75
  };

  if (performanceChartData.value) {
    return {
      ...baseData,
      chartData: performanceChartData.value
    };
  }

  return baseData;
});

onMounted(async () => {
  await loadAIPerformanceInsights();
  await loadPerformanceChartData();
  await loadLighthouseMetrics();
  await loadAdvancedMetrics();
  nextTick(() => {
    handleRecommendationsScroll();
    window.addEventListener('resize', handleRecommendationsScroll);
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleRecommendationsScroll);
});

async function reloadPerformanceData() {
  isReloading.value = true;
  try {
    const response = await fetch('/api/analyze-performance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: props.analysis.url,
        analysisId: props.analysis.id
      })
    });

    if (response.ok) {
      await loadAIPerformanceInsights();
      emit('refresh-analysis');
    }
  } catch (error) {
    console.error('Failed to reload performance data:', error);
  } finally {
    isReloading.value = false;
  }
}

async function loadAIPerformanceInsights() {
  if (!props.analysis) return;

  isLoadingAI.value = true;
  aiError.value = null;

  try {
  const { data: existingInsights, error: fetchError } = await supabase
    .from('performance_impact_insights')
    .select('*')
    .eq('analysis_id', props.analysis.id)
    .order('created_at', { ascending: true });

  if (fetchError) {
    console.error('Error fetching performance insights:', fetchError);
      throw fetchError;
  }

  if (existingInsights && existingInsights.length > 0) {
    const criticalInsight = existingInsights.find(insight => insight.impact_type === 'critical');
    const optimizationInsights = existingInsights.filter(insight => insight.impact_type === 'optimization');
    const opportunityInsights = existingInsights.filter(insight => insight.impact_type === 'opportunity');
    const primaryInsight = criticalInsight || existingInsights[0];

    aiPerformanceInsights.value = {
      primaryIssue: {
        title: primaryInsight?.metric_name || 'Performance Analysis Complete',
        description: primaryInsight?.impact_description || 'Performance metrics analyzed using AI insights',
        evidence: primaryInsight?.implementation_guide || 'Based on comprehensive performance analysis',
        conversionImpact: primaryInsight?.conversion_impact || 'Performance optimization can improve conversions'
      },
      keyFindings: optimizationInsights.length > 0 ? optimizationInsights.map(insight => ({
        metric: insight.metric_name,
        currentValue: insight.current_value?.toString() || 'Current',
        targetValue: insight.target_value?.toString() || 'Target',
        impact: insight.impact_description
      })) : [],
      recommendations: opportunityInsights.length > 0 ? opportunityInsights.map(insight => ({
        title: insight.metric_name,
        description: insight.implementation_guide || insight.impact_description,
        expectedImprovement: insight.expected_improvement || 'Performance improvement expected',
        priority: insight.effort_level || 'Medium'
      })) : [],
      businessImpact: existingInsights[0]?.conversion_impact || 'AI-powered performance analysis completed'
    };
    nextTick(handleRecommendationsScroll);
    return;
  }

    // Check if generation is in progress
    if ((props.analysis as any).generation_status === 'in_progress' || (props.analysis as any).generation_status === 'pending') {
      aiPerformanceInsights.value = {
        primaryIssue: {
          title: 'Performance Analysis In Progress',
          description: 'AI is currently analyzing your website performance metrics',
          evidence: 'Analysis will be available shortly',
          conversionImpact: 'Comprehensive insights are being generated'
        },
        keyFindings: [],
        recommendations: [],
        businessImpact: 'Performance analysis is being generated by AI'
      };
      return;
    }

    // No insights found - content may still be generating
    aiError.value = 'Performance insights are being generated. Please wait for the analysis to complete.';
    aiPerformanceInsights.value = null;

  } catch (error) {
    console.error('Error generating AI performance insights:', error);
    aiError.value = 'Failed to load performance insights. Please refresh the page or try again later.';
    aiPerformanceInsights.value = null;
  } finally {
    isLoadingAI.value = false;
  }
}

const performanceGradeColor = computed(() => {
  switch (props.analysis.performance_grade) {
    case 'A': return 'bg-green-500 text-white';
    case 'B': return 'bg-blue-500 text-white';
    case 'C': return 'bg-yellow-500 text-white';
    case 'D': return 'bg-orange-500 text-white';
    case 'F': return 'bg-red-500 text-white';
    default: return 'bg-gray-500 text-white';
  }
});

const coreWebVitals = computed(() => [
  { name: 'Largest Contentful Paint', value: props.analysis.lcp_score || 0, unit: 's', description: 'Time until largest element is rendered', status: evaluateMetric(props.analysis.lcp_score || 0, CORE_WEB_VITALS_THRESHOLDS.lcp) },
  { name: 'First Input Delay', value: props.analysis.fid_score || 0, unit: 'ms', description: 'Time to respond to user interaction', status: evaluateMetric(props.analysis.fid_score || 0, CORE_WEB_VITALS_THRESHOLDS.fid) },
  { name: 'Cumulative Layout Shift', value: props.analysis.cls_score || 0, unit: '', description: 'Visual stability during page load', status: evaluateMetric(props.analysis.cls_score || 0, CORE_WEB_VITALS_THRESHOLDS.cls) }
]);

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'text-green-600';
    case 'needs-improvement': return 'text-yellow-600';
    case 'poor': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

const formatValue = (value: number, unit: string) => {
  if (unit === 's') return `${value.toFixed(2)}s`;
  if (unit === 'ms') return `${Math.round(value)}ms`;
  return value.toFixed(3);
};

const toggleAdvancedMetrics = () => {
  showAdvancedMetrics.value = !showAdvancedMetrics.value;
};

const generateAdvancedSummary = async () => {
  if (isLoadingAdvancedSummary.value) return;
  isLoadingAdvancedSummary.value = true;
  advancedSummaryError.value = null;
  try {
    const response = await fetch('/api/generate-advanced-metrics-summary', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ analysisId: props.analysis.id })
    });
    const data = await response.json();
    if (!response.ok) throw new Error(data.error || 'Failed to generate advanced summary');
    advancedSummary.value = data.summary;
  } catch (error) {
    console.error('Error generating advanced summary:', error);
    advancedSummaryError.value = error instanceof Error ? error.message : 'Failed to generate summary';
  } finally {
    isLoadingAdvancedSummary.value = false;
  }
};

const toggleTooltip = (tooltipId: string) => {
  showTooltip.value = showTooltip.value === tooltipId ? null : tooltipId;
};

const getTooltipContent = (metric: string) => {
  const tooltips = {
    lcp: 'Largest Contentful Paint measures how long it takes for the main content to load. Good: ≤2.5s, Needs Improvement: ≤4s, Poor: >4s',
    fid: 'First Input Delay measures how long it takes for the page to respond to user interactions. Good: ≤100ms, Needs Improvement: ≤300ms, Poor: >300ms',
    cls: 'Cumulative Layout Shift measures visual stability - how much content moves around. Good: ≤0.1, Needs Improvement: ≤0.25, Poor: >0.25',
    performance: 'Overall performance score based on multiple factors including loading speed, interactivity, and visual stability. Scored 0-100.'
  };
  return tooltips[metric as keyof typeof tooltips] || '';
};

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'high': return 'bg-red-100 text-red-800';
    case 'medium': return 'bg-yellow-100 text-yellow-800';
    case 'low': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const lighthouseMetrics = computed(() => {
  // First try to use the loaded lighthouse metrics data
  if (lighthouseMetricsData.value?.metrics) {
    return lighthouseMetricsData.value.metrics;
  }

  // Fallback to analysis lighthouse_data
  if (!props.analysis.lighthouse_data) return null;
  try {
    const data = typeof props.analysis.lighthouse_data === 'string'
      ? JSON.parse(props.analysis.lighthouse_data)
      : props.analysis.lighthouse_data;
    if (data.performanceScore !== undefined) {
      return {
        performanceScore: data.performanceScore,
        accessibilityScore: data.accessibilityScore,
        bestPracticesScore: data.bestPracticesScore,
        seoScore: data.seoScore
      };
    }
    if (data.categories) {
      return {
        performanceScore: Math.round((data.categories.performance?.score || 0) * 100),
        accessibilityScore: Math.round((data.categories.accessibility?.score || 0) * 100),
        bestPracticesScore: Math.round((data.categories['best-practices']?.score || 0) * 100),
        seoScore: Math.round((data.categories.seo?.score || 0) * 100)
      };
    }
    return null;
  } catch (error) {
    console.error('Error parsing lighthouse data:', error);
    return null;
  }
});

const actualPerformanceScore = computed(() => {
  const lighthouseData = props.analysis.lighthouse_data;
  if (lighthouseData && typeof lighthouseData === 'object') {
    return lighthouseData.performanceScore || props.analysis.performance_score || 0;
  }
  return props.analysis.performance_score || 0;
});

const actualPerformanceGrade = computed(() => {
  const score = actualPerformanceScore.value;
  if (score >= 90) return 'A';
  if (score >= 80) return 'B';
  if (score >= 70) return 'C';
  if (score >= 60) return 'D';
  return 'F';
});

const performanceScoreColor = computed(() => {
  const score = actualPerformanceScore.value;
  if (score >= 90) return 'text-green-500';
  if (score >= 80) return 'text-blue-500';
  if (score >= 70) return 'text-yellow-500';
  if (score >= 60) return 'text-orange-500';
  return 'text-red-500';
});

// --- Carousel Methods ---
const handleRecommendationsScroll = () => {
  if (!recommendationsContainer.value) return;
  const { scrollLeft, scrollWidth, clientWidth } = recommendationsContainer.value;
  const tolerance = 1;
  showLeftArrow.value = scrollLeft > tolerance;
  showRightArrow.value = scrollLeft < scrollWidth - clientWidth - tolerance;
};

const scrollRecommendations = (direction: 'left' | 'right') => {
  if (!recommendationsContainer.value) return;
  const container = recommendationsContainer.value;
  const cards = Array.from(container.children) as HTMLElement[];
  const cardWidth = cards[0]?.offsetWidth || 0;
  const currentScroll = container.scrollLeft;
  const containerCenter = currentScroll + container.clientWidth / 2;

  let targetCard;

  if (direction === 'right') {
    targetCard = cards.find(card => card.offsetLeft + card.offsetWidth / 2 > containerCenter + cardWidth / 2);
    if (!targetCard) targetCard = cards[cards.length - 1];
  } else {
    targetCard = cards.slice().reverse().find(card => card.offsetLeft + card.offsetWidth / 2 < containerCenter - cardWidth / 2);
    if (!targetCard) targetCard = cards[0];
  }

  if (targetCard) {
    container.scrollTo({
      left: targetCard.offsetLeft - (container.clientWidth - targetCard.offsetWidth) / 2,
      behavior: 'smooth'
    });
  }
};

const startDragging = (e: MouseEvent) => {
  if (!recommendationsContainer.value) return;
  isDragging.value = true;
  startX.value = e.pageX - recommendationsContainer.value.offsetLeft;
  scrollLeft.value = recommendationsContainer.value.scrollLeft;
  recommendationsContainer.value.style.cursor = 'grabbing';
  recommendationsContainer.value.style.scrollSnapType = 'none';
};

const stopDragging = () => {
  if (!isDragging.value) return;
  isDragging.value = false;
  if (recommendationsContainer.value) {
    recommendationsContainer.value.style.cursor = 'grab';
    recommendationsContainer.value.style.scrollSnapType = 'x mandatory';
    // Snap to nearest card after drag
    const container = recommendationsContainer.value;
    const cards = Array.from(container.children) as HTMLElement[];
    const containerCenter = container.scrollLeft + container.clientWidth / 2;
    
    const closestCard = cards.reduce((prev, curr) => {
      const prevCenter = prev.offsetLeft + prev.offsetWidth / 2;
      const currCenter = curr.offsetLeft + curr.offsetWidth / 2;
      return (Math.abs(currCenter - containerCenter) < Math.abs(prevCenter - containerCenter) ? curr : prev);
    });

    container.scrollTo({
      left: closestCard.offsetLeft - (container.clientWidth - closestCard.offsetWidth) / 2,
      behavior: 'smooth'
    });
  }
};

const onDrag = (e: MouseEvent) => {
  if (!isDragging.value || !recommendationsContainer.value) return;
  e.preventDefault();
  const x = e.pageX - recommendationsContainer.value.offsetLeft;
  const walk = (x - startX.value) * 2;
  recommendationsContainer.value.scrollLeft = scrollLeft.value - walk;
};

</script>

<template>
  <div class="space-y-6">
    <!-- AI Performance Impact Analysis -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <TrendingUp class="w-5 h-5 text-gray-400 mr-2" />
          <h3 class="text-lg font-semibold text-gray-900">AI Performance Impact Analysis</h3>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingAI" class="text-center py-12">
        <Loader2 class="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
        <p class="text-gray-600">Analyzing performance data to generate contextual insights...</p>
      </div>

      <!-- AI Performance Insights -->
      <div v-else-if="aiPerformanceInsights">
        <!-- Primary Issue Analysis -->
        <div class="bg-red-50 rounded-lg p-4 border border-red-200 mb-6">
          <h4 class="font-medium text-red-900 mb-3 flex items-center">
            <AlertTriangle class="w-4 h-4 mr-2" />
            {{ aiPerformanceInsights.primaryIssue.title }}
          </h4>
          <p class="text-red-800 text-sm mb-3">{{ aiPerformanceInsights.primaryIssue.description }}</p>
          <div class="space-y-2 text-xs">
            <p class="text-red-700"><strong>Evidence:</strong> {{ aiPerformanceInsights.primaryIssue.evidence }}</p>
            <p class="text-red-700"><strong>Conversion Impact:</strong> {{ aiPerformanceInsights.primaryIssue.conversionImpact }}</p>
          </div>
        </div>

        <!-- Performance Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <!-- Overall Performance Score -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-500 mb-1">Overall Performance Score</p>
                <div class="flex items-center space-x-3">
                  <span class="text-3xl font-bold" :class="performanceScoreColor">
                    {{ actualPerformanceScore }}
                  </span>
                  <span class="text-gray-500">/100</span>
                  <span
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                    :class="performanceGradeColor"
                  >
                    Grade {{ actualPerformanceGrade }}
                  </span>
                </div>
              </div>
              <button
                @click="reloadPerformanceData"
                :disabled="isReloading"
                class="btn-secondary text-sm flex items-center"
              >
                <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': isReloading }]" />
                {{ isReloading ? 'Reloading...' : 'Reload' }}
              </button>
            </div>

            <!-- AI Business Impact -->
            <div class="bg-blue-50 rounded-lg p-4">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <div>
                  <h4 class="font-medium text-blue-900 mb-1">AI Business Impact</h4>
                  <p class="text-sm text-blue-700">{{ aiPerformanceInsights.businessImpact }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Core Web Vitals -->
          <div class="space-y-4">
            <div
              v-for="vital in coreWebVitals"
              :key="vital.name"
              class="relative flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
            >
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <div class="flex items-center">
                    <h5 class="font-medium text-gray-900 text-sm">{{ vital.name }}</h5>
                    <button
                      @click="toggleTooltip(vital.name.toLowerCase().replace(/\s+/g, ''))"
                      class="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    >
                      <HelpCircle class="w-3 h-3" />
                    </button>
                  </div>
                  <span
                    class="text-lg font-bold"
                    :class="getStatusColor(vital.status)"
                  >
                    {{ formatValue(vital.value, vital.unit) }}
                  </span>
                </div>
                <p class="text-xs text-gray-500">{{ vital.description }}</p>

                <!-- Tooltip -->
                <div
                  v-if="showTooltip === vital.name.toLowerCase().replace(/\s+/g, '')"
                  class="absolute z-30 bg-white border border-gray-200 rounded-lg shadow-lg p-3 text-xs max-w-xs left-0 right-0 text-gray-700"
                  style="bottom: 100%; margin-bottom: 8px;"
                >
                  {{ getTooltipContent(vital.name.toLowerCase().replace(/\s+/g, '').substring(0, 3)) }}
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Key Findings -->
        <div v-if="aiPerformanceInsights.keyFindings.length > 0" class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h4 class="font-medium text-gray-900 mb-4 flex items-center">
            <Info class="w-4 h-4 mr-2" />
            AI-Identified Key Findings
          </h4>
          <div class="space-y-3">
            <div 
              v-for="(finding, index) in aiPerformanceInsights.keyFindings" 
              :key="index"
              class="p-3 bg-blue-50 rounded-lg border border-blue-200"
            >
              <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-blue-900 text-sm">{{ finding.metric }}</h5>
                <div class="text-xs text-blue-700">
                  {{ finding.currentValue }} → {{ finding.targetValue }}
                </div>
              </div>
              <p class="text-sm text-blue-800">{{ finding.impact }}</p>
            </div>
          </div>
        </div>

        <!-- AI Recommendations Carousel -->
        <div v-if="aiPerformanceInsights.recommendations.length > 0" class="relative">
          <div class="flex items-center mb-4 px-6">
            <CheckCircle class="w-5 h-5 text-gray-400 mr-2" />
            <h3 class="text-lg font-semibold text-gray-900">AI Performance Recommendations</h3>
          </div>

          <div class="relative group">
            <button
              @click="scrollRecommendations('left')"
              v-show="showLeftArrow"
              class="absolute top-1/2 -translate-y-1/2 left-0 z-10 bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 transition-opacity opacity-0 group-hover:opacity-100 disabled:opacity-0"
            >
              <ChevronLeft class="w-5 h-5" />
            </button>

            <div
              ref="recommendationsContainer"
              @scroll="handleRecommendationsScroll"
              @mousedown.prevent="startDragging"
              @mouseleave="stopDragging"
              @mouseup="stopDragging"
              @mousemove="onDrag"
              class="flex overflow-x-auto scroll-smooth snap-x snap-mandatory scrollbar-hide py-4"
              style="cursor: grab; padding-left: 5%; padding-right: 5%;"
            >
              <div
                v-for="(recommendation, index) in aiPerformanceInsights.recommendations"
                :key="index"
                class="snap-center flex-shrink-0 w-4/5 md:w-4/4 lg:w-3/3 border border-gray-200 rounded-lg p-5 flex flex-col mx-2"
                style="padding-bottom: calc(1.25rem * 1.2);"
              >
                <div class="flex items-start justify-between mb-3">
                  <h4 class="font-medium text-gray-900">{{ recommendation.title }}</h4>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ml-2"
                    :class="getPriorityColor(recommendation.priority)"
                  >
                    {{ recommendation.priority }} Priority
                  </span>
                </div>
                <p class="text-gray-700 text-sm mb-3 flex-grow">{{ recommendation.description }}</p>
                <div class="bg-green-50 rounded p-3 border border-green-200 mt-auto">
                  <p class="text-green-800 text-sm">
                    <strong>Expected Improvement:</strong> {{ recommendation.expectedImprovement }}
                  </p>
                </div>
              </div>
            </div>

            <button
              @click="scrollRecommendations('right')"
              v-show="showRightArrow"
              class="absolute top-1/2 -translate-y-1/2 right-0 z-10 bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 transition-opacity opacity-0 group-hover:opacity-100 disabled:opacity-0"
            >
              <ChevronRight class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="aiError" class="text-center py-12">
        <AlertTriangle class="w-8 h-8 text-yellow-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">AI Analysis Unavailable</h3>
        <p class="text-gray-600 mb-4">{{ aiError }}</p>
        <button 
          @click="loadAIPerformanceInsights" 
          class="btn-primary"
        >
          Retry AI Analysis
        </button>
      </div>
    </div>

    <!-- Advanced Metrics Toggle -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
      <button
        @click="toggleAdvancedMetrics"
        class="flex items-center justify-between w-full text-left"
      >
        <span class="font-medium text-gray-900">Advanced Metrics</span>
        <svg 
          class="w-5 h-5 text-gray-400 transition-transform duration-300"
          :class="{ 'rotate-180': showAdvancedMetrics }"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div 
        v-if="showAdvancedMetrics"
        class="mt-4 p-4 bg-gray-50 rounded-lg transition-all duration-300 ease-in-out"
      >
        <!-- ... (rest of advanced metrics content is unchanged) ... -->
      </div>
    </div>

    <!-- Performance Content Breakdown Chart -->
    <PerformanceBreakdownChart
      :performance-data="enhancedPerformanceData"
      :lighthouse-data="lighthouseMetrics"
    />
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.btn-secondary {
  @apply inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.btn-secondary:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.rotate-180 {
  transform: rotate(180deg);
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
