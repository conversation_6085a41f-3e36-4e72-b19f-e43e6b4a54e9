import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { 
  generateAIProsConsInsights, 
  generateAILeadInsights, 
  generateAIPerformanceInsights,
  generateAIPageSummary,
  generateAIPerformanceSEOSummary
} from '../../lib/ai-contextual-analyzer';
import { log } from '../../lib/logger';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId, insightType } = await request.json();

    log.info(`Received AI insights generation request for analysis: ${analysisId}, type: ${insightType}`);
    log.info(`Full request body: ${JSON.stringify(await request.clone().json())}`);
    log.info(`Full request body: ${JSON.stringify(await request.clone().json())}`);

    if (!analysisId) {
      log.warn('Missing analysisId in AI insights request');
      return new Response(JSON.stringify({ error: 'Missing analysisId' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch analysis data
    const { data: analysis, error: fetchError } = await supabaseAdmin
      .from('analyses')
      .select('*')
      .eq('id', analysisId)
      .single();

    if (fetchError || !analysis) {
      log.error(`Failed to fetch analysis: ${fetchError?.message}`);
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    let result = {};

    switch (insightType) {
      case 'pros-cons':
        log.info('Generating AI pros/cons insights...');
        const prosConsInsights = await generateAIProsConsInsights(analysis);
        
        // Store insights in database
        const { data: categories } = await supabaseAdmin
          .from('insight_categories')
          .select('id, name');

        const categoryMap = new Map(categories?.map(cat => [cat.name, cat.id]) || []);

        // Store strengths
        if (prosConsInsights.strengths.length > 0) {
          const strengthsToInsert = prosConsInsights.strengths.map(strength => ({
            analysis_id: analysisId,
            category_id: categoryMap.get(strength.category) || categoryMap.get('conversion'),
            insight_type: 'strength',
            title: strength.title,
            description: strength.description,
            evidence: strength.evidence,
            impact_explanation: strength.impactExplanation,
            business_value: strength.businessValue,
            priority_score: strength.priorityScore,
            confidence_score: strength.confidenceScore || 85
          }));

          await supabaseAdmin.from('ai_insights').insert(strengthsToInsert);
        }

        // Store weaknesses
        if (prosConsInsights.weaknesses.length > 0) {
          const weaknessesToInsert = prosConsInsights.weaknesses.map(weakness => ({
            analysis_id: analysisId,
            category_id: categoryMap.get(weakness.category) || categoryMap.get('conversion'),
            insight_type: 'weakness',
            title: weakness.title,
            description: weakness.description,
            evidence: weakness.evidence,
            impact_explanation: weakness.impactExplanation,
            implementation_steps: weakness.implementationSteps,
            business_value: weakness.businessValue,
            priority_score: weakness.priorityScore,
            confidence_score: weakness.confidenceScore || 85
          }));

          await supabaseAdmin.from('ai_insights').insert(weaknessesToInsert);
        }
        
        result = {
          type: 'pros-cons',
          data: prosConsInsights
        };
        break;

      case 'lead-insights':
        log.info('Generating AI lead insights...');
        const leadInsights = await generateAILeadInsights(analysis);
        
        // Store lead insights in database
        if (leadInsights.leadQuestions.length > 0) {
          const leadQuestionsToInsert = leadInsights.leadQuestions.map(question => ({
            analysis_id: analysisId,
            question_type: question.questionType,
            question_text: question.questionText,
            context_explanation: question.contextExplanation,
            qualification_value: question.qualificationValue,
            suggested_response: question.suggestedResponse,
            priority_level: question.priorityLevel
          }));

          await supabaseAdmin.from('lead_qualification_insights').insert(leadQuestionsToInsert);
        }
        
        result = {
          type: 'lead-insights',
          data: leadInsights
        };
        break;

      case 'performance-insights':
        log.info('Generating AI performance insights...');
        const performanceInsights = await generateAIPerformanceInsights(analysis);
        
        // Store performance insights in database
        if (performanceInsights.keyFindings.length > 0) {
          const performanceToInsert = performanceInsights.keyFindings.map((finding, index) => ({
            analysis_id: analysisId,
            impact_type: index === 0 ? 'critical' : 'opportunity',
            metric_name: finding.metric,
            current_value: parseFloat(finding.currentValue) || 0,
            target_value: parseFloat(finding.targetValue) || 0,
            impact_description: finding.impact,
            conversion_impact: performanceInsights.businessImpact,
            implementation_guide: performanceInsights.recommendations[index]?.description || 'Implementation guide available',
            expected_improvement: performanceInsights.recommendations[index]?.expectedImprovement || 'Improvement expected',
            effort_level: performanceInsights.recommendations[index]?.priority || 'Medium',
            timeline: '2-4 weeks'
          }));

          await supabaseAdmin.from('performance_impact_insights').insert(performanceToInsert);
        }
        
        result = {
          type: 'performance-insights',
          data: performanceInsights
        };
        break;

      case 'page-summary':
        log.info('Generating AI page summary...');
        const pageSummary = await generateAIPageSummary(analysis);
        
        // Store page summary in database
        await supabaseAdmin
          .from('ai_page_summaries')
          .upsert({
            analysis_id: analysisId,
            page_purpose: pageSummary.pagePurpose,
            key_takeaways: pageSummary.keyTakeaways,
            hero_effectiveness: pageSummary.heroEffectiveness,
            value_proposition_clarity: pageSummary.valuePropositionClarity,
            lead_capture_assessment: pageSummary.leadCaptureAssessment,
            user_journey_analysis: pageSummary.userJourneyAnalysis,
            business_impact_summary: pageSummary.businessImpactSummary
          });
        
        result = {
          type: 'page-summary',
          data: pageSummary
        };
        break;

      case 'performance-seo-summary':
        log.info('Generating AI performance & SEO summary...');
        const performanceSEOSummary = await generateAIPerformanceSEOSummary(analysis);
        
        result = {
          type: 'performance-seo-summary',
          data: performanceSEOSummary
        };
        break;

      case 'all':
        log.info('Generating all AI insights...');
        
        // Generate all insights in parallel
        const [prosConsResult, leadResult, performanceResult, summaryResult, perfSeoResult] = await Promise.allSettled([
          generateAIProsConsInsights(analysis),
          generateAILeadInsights(analysis),
          generateAIPerformanceInsights(analysis),
          generateAIPageSummary(analysis),
          generateAIPerformanceSEOSummary(analysis)
        ]);

        // Store successful results
        if (prosConsResult.status === 'fulfilled') {
          const { data: categories } = await supabaseAdmin
            .from('insight_categories')
            .select('id, name');

          const categoryMap = new Map(categories?.map(cat => [cat.name, cat.id]) || []);

          const allInsights = [
            ...prosConsResult.value.strengths.map(s => ({ 
              ...s, 
              insightType: 'strength' as const,
              category_id: categoryMap.get(s.category) || categoryMap.get('conversion')
            })),
            ...prosConsResult.value.weaknesses.map(w => ({ 
              ...w, 
              insightType: 'weakness' as const,
              category_id: categoryMap.get(w.category) || categoryMap.get('conversion')
            }))
          ];

          const insightsToInsert = allInsights.map(insight => ({
            analysis_id: analysisId,
            category_id: insight.category_id,
            insight_type: insight.insightType,
            title: insight.title,
            description: insight.description,
            evidence: insight.evidence,
            impact_explanation: insight.impactExplanation,
            implementation_steps: insight.implementationSteps || null,
            business_value: insight.businessValue,
            priority_score: insight.priorityScore,
            confidence_score: insight.confidenceScore || 85
          }));

          await supabaseAdmin.from('ai_insights').insert(insightsToInsert);
        }

        if (leadResult.status === 'fulfilled') {
          const leadQuestionsToInsert = leadResult.value.leadQuestions.map(question => ({
            analysis_id: analysisId,
            question_type: question.questionType,
            question_text: question.questionText,
            context_explanation: question.contextExplanation,
            qualification_value: question.qualificationValue,
            suggested_response: question.suggestedResponse,
            priority_level: question.priorityLevel
          }));

          await supabaseAdmin.from('lead_qualification_insights').insert(leadQuestionsToInsert);
        }

        if (summaryResult.status === 'fulfilled') {
          await supabaseAdmin
            .from('ai_page_summaries')
            .upsert({
              analysis_id: analysisId,
              page_purpose: summaryResult.value.pagePurpose,
              key_takeaways: summaryResult.value.keyTakeaways,
              hero_effectiveness: summaryResult.value.heroEffectiveness,
              value_proposition_clarity: summaryResult.value.valuePropositionClarity,
              lead_capture_assessment: summaryResult.value.leadCaptureAssessment,
              user_journey_analysis: summaryResult.value.userJourneyAnalysis,
              business_impact_summary: summaryResult.value.businessImpactSummary
            });
        }

        // Mark AI insights as generated
        await supabaseAdmin
          .from('analyses')
          .update({ 
            ai_insights_generated: true,
            ai_summary_generated: true 
          })
          .eq('id', analysisId);

        result = {
          type: 'all',
          data: {
            prosConsInsights: prosConsResult.status === 'fulfilled' ? prosConsResult.value : null,
            leadInsights: leadResult.status === 'fulfilled' ? leadResult.value : null,
            performanceInsights: performanceResult.status === 'fulfilled' ? performanceResult.value : null,
            pageSummary: summaryResult.status === 'fulfilled' ? summaryResult.value : null,
            performanceSEOSummary: perfSeoResult.status === 'fulfilled' ? perfSeoResult.value : null
          }
        };
        break;

      default:
        return new Response(JSON.stringify({ error: 'Invalid insight type' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
    }

    return new Response(JSON.stringify({
      success: true,
      result
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`AI insights generation error: ${error instanceof Error ? error.message : error}`);

    return new Response(JSON.stringify({
      error: 'AI insights generation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};