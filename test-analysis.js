// Simple test script to trigger analysis for a pending analysis
const analysisId = '793cb160-507b-4c14-aff2-01e87f9ca75b';
const url = 'https://github.com/';

console.log('Testing comprehensive analysis...');

// Test the comprehensive analysis API
fetch('http://localhost:4321/api/comprehensive-analysis', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    url: url,
    analysisId: analysisId
  })
})
.then(response => {
  console.log('Response status:', response.status);
  return response.json();
})
.then(data => {
  console.log('Response data:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
