<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import type { Database } from '../../types/supabase';
import { 
  Info,
  AlertCircle,
  TrendingUp,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next';

type Suggestion = Database['public']['Tables']['suggestions']['Row'];

const props = defineProps<{
  suggestions: Suggestion[];
}>();

const suggestionsContainer = ref<HTMLElement | null>(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
const isDragging = ref(false);
const startX = ref(0);
const scrollLeft = ref(0);

onMounted(() => {
  nextTick(() => {
    handleScroll();
    window.addEventListener('resize', handleScroll);
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleScroll);
});

const handleScroll = () => {
  if (!suggestionsContainer.value) return;
  const { scrollLeft, scrollWidth, clientWidth } = suggestionsContainer.value;
  const tolerance = 1;
  showLeftArrow.value = scrollLeft > tolerance;
  showRightArrow.value = scrollLeft < scrollWidth - clientWidth - tolerance;
};

const scroll = (direction: 'left' | 'right') => {
  if (!suggestionsContainer.value) return;
  const container = suggestionsContainer.value;
  const cards = Array.from(container.children) as HTMLElement[];
  const cardWidth = cards[0]?.offsetWidth || 0;
  const currentScroll = container.scrollLeft;
  const containerCenter = currentScroll + container.clientWidth / 2;

  let targetCard;

  if (direction === 'right') {
    targetCard = cards.find(card => card.offsetLeft + card.offsetWidth / 2 > containerCenter + cardWidth / 2);
    if (!targetCard) targetCard = cards[cards.length - 1];
  } else {
    targetCard = cards.slice().reverse().find(card => card.offsetLeft + card.offsetWidth / 2 < containerCenter - cardWidth / 2);
    if (!targetCard) targetCard = cards[0];
  }

  if (targetCard) {
    container.scrollTo({
      left: targetCard.offsetLeft - (container.clientWidth - targetCard.offsetWidth) / 2,
      behavior: 'smooth'
    });
  }
};

const startDragging = (e: MouseEvent) => {
  if (!suggestionsContainer.value) return;
  isDragging.value = true;
  startX.value = e.pageX - suggestionsContainer.value.offsetLeft;
  scrollLeft.value = suggestionsContainer.value.scrollLeft;
  suggestionsContainer.value.style.cursor = 'grabbing';
  suggestionsContainer.value.style.scrollSnapType = 'none';
};

const stopDragging = () => {
  if (!isDragging.value) return;
  isDragging.value = false;
  if (suggestionsContainer.value) {
    suggestionsContainer.value.style.cursor = 'grab';
    suggestionsContainer.value.style.scrollSnapType = 'x mandatory';
    const container = suggestionsContainer.value;
    const cards = Array.from(container.children) as HTMLElement[];
    const containerCenter = container.scrollLeft + container.clientWidth / 2;
    
    const closestCard = cards.reduce((prev, curr) => {
      const prevCenter = prev.offsetLeft + prev.offsetWidth / 2;
      const currCenter = curr.offsetLeft + curr.offsetWidth / 2;
      return (Math.abs(currCenter - containerCenter) < Math.abs(prevCenter - containerCenter) ? curr : prev);
    });

    container.scrollTo({
      left: closestCard.offsetLeft - (container.clientWidth - closestCard.offsetWidth) / 2,
      behavior: 'smooth'
    });
  }
};

const onDrag = (e: MouseEvent) => {
  if (!isDragging.value || !suggestionsContainer.value) return;
  e.preventDefault();
  const x = e.pageX - suggestionsContainer.value.offsetLeft;
  const walk = (x - startX.value) * 2;
  suggestionsContainer.value.scrollLeft = scrollLeft.value - walk;
};

const getImpactColor = (impact: string) => {
  switch (impact.toLowerCase()) {
    case 'high': return 'bg-red-100 text-red-800 border-red-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getEffortColor = (effort: string) => {
  switch (effort.toLowerCase()) {
    case 'high': return 'text-red-600';
    case 'medium': return 'text-yellow-600';
    case 'low': return 'text-green-600';
    default: return 'text-gray-600';
  }
};

const getImpactIcon = (impact: string) => {
  switch (impact.toLowerCase()) {
    case 'high': return AlertCircle;
    case 'medium': return TrendingUp;
    case 'low': return Info;
    default: return Info;
  }
};

const getSuggestionHeader = (suggestion: any) => {
  if (suggestion.suggestion_headers && suggestion.suggestion_headers.length > 0) {
    return suggestion.suggestion_headers[0].ai_generated_title;
  }
  return suggestion.title;
};

</script>

<template>
  <div class="relative group">
    <button
        @click="scroll('left')"
        v-show="showLeftArrow"
        class="absolute top-1/2 -translate-y-1/2 left-0 z-10 bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 transition-opacity opacity-0 group-hover:opacity-100 disabled:opacity-0"
    >
        <ChevronLeft class="w-5 h-5" />
    </button>

    <div
        ref="suggestionsContainer"
        @scroll="handleScroll"
        @mousedown.prevent="startDragging"
        @mouseleave="stopDragging"
        @mouseup="stopDragging"
        @mousemove="onDrag"
        class="flex overflow-x-auto scroll-smooth snap-x snap-mandatory scrollbar-hide py-4"
        style="cursor: grab; padding-left: 5%; padding-right: 5%;"
    >
        <div 
            v-for="suggestion in suggestions" 
            :key="suggestion.id" 
            class="snap-center flex-shrink-0 w-4/5 md:w-3/4 lg:w-2/3 border border-gray-200 rounded-lg p-6 flex flex-col mx-2"
        >
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <div class="flex items-center mb-3">
                        <span 
                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border mr-3"
                            :class="getImpactColor(suggestion.impact_level)"
                        >
                            <component :is="getImpactIcon(suggestion.impact_level)" class="w-3 h-3 mr-1" />
                            {{ suggestion.impact_level }} Impact
                        </span>
                        <span 
                            class="text-xs font-medium"
                            :class="getEffortColor(suggestion.effort_level)"
                        >
                            {{ suggestion.effort_level }} Effort
                        </span>
                    </div>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">
                        {{ getSuggestionHeader(suggestion) }}
                    </h4>
                </div>
            </div>
            <div class="text-gray-700 leading-relaxed mb-4 flex-grow">
                <p>{{ suggestion.description }}</p>
            </div>
            <div v-if="suggestion.detailed_explanation" class="bg-blue-50 rounded-lg p-4 border border-blue-200 mt-auto">
                <h5 class="font-medium text-blue-900 mb-2 flex items-center">
                    <Info class="w-4 h-4 mr-2" />
                    Implementation Tips
                </h5>
                <p class="text-blue-800 text-sm leading-relaxed">{{ suggestion.detailed_explanation }}</p>
            </div>
        </div>
    </div>

    <button
        @click="scroll('right')"
        v-show="showRightArrow"
        class="absolute top-1/2 -translate-y-1/2 right-0 z-10 bg-white/80 backdrop-blur-sm rounded-full shadow-md w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 transition-opacity opacity-0 group-hover:opacity-100 disabled:opacity-0"
    >
        <ChevronRight class="w-5 h-5" />
    </button>
  </div>
</template>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
