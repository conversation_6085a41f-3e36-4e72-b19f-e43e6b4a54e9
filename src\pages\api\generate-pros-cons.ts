import type { APIRoute } from 'astro';
import { supabaseAdmin } from '../../lib/supabase-client';
import { generateAIProsConsInsights } from '../../lib/ai-contextual-analyzer';
import { log } from '../../lib/logger';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { analysisId } = await request.json();

    if (!analysisId) {
      return new Response(JSON.stringify({ error: 'Missing analysisId' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    log.info(`Generating pros & cons for analysis ${analysisId}`);

    // Get the analysis data
    const { data: analysis, error: analysisError } = await supabaseAdmin
      .from('analyses')
      .select('*')
      .eq('id', analysisId)
      .single();

    if (analysisError || !analysis) {
      return new Response(JSON.stringify({ error: 'Analysis not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate AI insights
    const data = await generateAIProsConsInsights(analysis);

    // Get suggestion categories
    let { data: categories } = await supabaseAdmin
      .from('suggestion_categories')
      .select('id, name');

    // Create default categories if none exist
    if (!categories || categories.length === 0) {
      const defaultCategories = [
        { name: 'conversion', icon: 'target', color: 'blue', description: 'Conversion optimization improvements' },
        { name: 'performance', icon: 'zap', color: 'green', description: 'Performance and speed improvements' },
        { name: 'seo', icon: 'search', color: 'purple', description: 'SEO and visibility improvements' },
        { name: 'ux', icon: 'user', color: 'orange', description: 'User experience improvements' },
        { name: 'content', icon: 'file-text', color: 'red', description: 'Content and messaging improvements' }
      ];

      const { data: insertedCategories } = await supabaseAdmin
        .from('suggestion_categories')
        .insert(defaultCategories)
        .select('id, name');

      categories = insertedCategories || [];
    }

    const categoryMap = new Map(categories?.map(cat => [cat.name, cat.id]) || []);
    let defaultCategoryId = categoryMap.get('conversion');
    if (!defaultCategoryId && categories && categories.length > 0) {
      defaultCategoryId = categories[0].id;
    }

    // Store strengths
    if (data.strengths && data.strengths.length > 0) {
      const strengthsToInsert = data.strengths.map((strength: any, index: number) => ({
        analysis_id: analysisId,
        category_id: categoryMap.get(strength.category) || defaultCategoryId,
        insight_type: 'strength',
        title: strength.title,
        description: strength.description,
        evidence: strength.evidence,
        impact_explanation: strength.impactExplanation,
        implementation_steps: strength.implementationSteps,
        business_value: strength.businessValue,
        priority_score: strength.priorityScore,
        confidence_score: strength.confidenceScore,
        display_order: index,
        impact_score: Math.round((strength.priorityScore || 5) / 10 * 10),
        effort_required: strength.effortRequired || 'Medium',
        timeline_estimate: strength.timelineEstimate || 'To be determined'
      }));

      await supabaseAdmin
        .from('ai_insights')
        .upsert(strengthsToInsert);
    }

    // Store weaknesses
    if (data.weaknesses && data.weaknesses.length > 0) {
      const weaknessesToInsert = data.weaknesses.map((weakness: any, index: number) => ({
        analysis_id: analysisId,
        category_id: categoryMap.get(weakness.category) || defaultCategoryId,
        insight_type: 'weakness',
        title: weakness.title,
        description: weakness.description,
        evidence: weakness.evidence,
        impact_explanation: weakness.impactExplanation,
        implementation_steps: weakness.implementationSteps,
        business_value: weakness.businessValue,
        priority_score: weakness.priorityScore,
        confidence_score: weakness.confidenceScore,
        display_order: index,
        impact_score: Math.round((weakness.priorityScore || 5) / 10 * 10),
        effort_required: weakness.effortRequired || 'Medium',
        timeline_estimate: weakness.timelineEstimate || 'To be determined'
      }));

      await supabaseAdmin
        .from('ai_insights')
        .upsert(weaknessesToInsert);
    }

    // Mark as generated
    await supabaseAdmin
      .from('analyses')
      .update({ pros_cons_generated: true })
      .eq('id', analysisId);

    log.info(`Successfully generated and stored pros & cons for analysis ${analysisId}`);

    return new Response(JSON.stringify({
      success: true,
      strengthsCount: data.strengths?.length || 0,
      weaknessesCount: data.weaknesses?.length || 0,
      overallAssessment: data.overallAssessment
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    log.error(`Error generating pros & cons: ${error}`);
    
    return new Response(JSON.stringify({
      error: 'Failed to generate pros & cons',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
