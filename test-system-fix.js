// Complete system test for ConvertIQ fixes
const testAnalysisId = '05581703-f5a5-433c-8ce8-d6452d6724c0'; // The failing analysis ID

console.log('🔧 Testing ConvertIQ System Fixes');
console.log('Analysis ID:', testAnalysisId);

// Test 1: Check database schema fixes
async function testDatabaseSchema() {
  console.log('\n📋 Test 1: Testing database schema fixes...');
  
  try {
    // Test if we can fetch analysis progress (tests schema)
    const response = await fetch(`http://localhost:4321/api/analysis-progress?analysisId=${testAnalysisId}`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Database schema working');
      console.log('Current status:', data.status);
      return true;
    } else {
      console.log('❌ Database schema issue:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Database schema error:', error.message);
    return false;
  }
}

// Test 2: Test AI insights generation fix
async function testAIInsightsGeneration() {
  console.log('\n🤖 Test 2: Testing AI insights generation fix...');
  
  try {
    const response = await fetch('http://localhost:4321/api/generate-ai-insights-sequential', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        analysisId: testAnalysisId
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ AI insights generation working');
      return true;
    } else {
      console.log('❌ AI insights generation failed:', data.error || data.details);
      return false;
    }
  } catch (error) {
    console.log('❌ Error in AI insights generation:', error.message);
    return false;
  }
}

// Test 3: Test individual step retry
async function testStepRetry() {
  console.log('\n🔄 Test 3: Testing individual step retry...');
  
  try {
    const response = await fetch('http://localhost:4321/api/retry-analysis-step', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        analysisId: testAnalysisId,
        stepNumber: 3 // Test pros-cons step
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Step retry working');
      console.log('Retried step:', data.stepName);
      return true;
    } else {
      console.log('❌ Step retry failed:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error in step retry:', error.message);
    return false;
  }
}

// Test 4: Test comprehensive analysis with new analysis
async function testNewAnalysis() {
  console.log('\n🆕 Test 4: Testing new analysis creation...');
  
  try {
    const response = await fetch('http://localhost:4321/api/comprehensive-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://stripe.com/',
        analysisId: 'test-' + Date.now()
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ New analysis creation working');
      console.log('Suggestions generated:', data.suggestionsCount);
      return true;
    } else {
      console.log('❌ New analysis creation failed:', data.error || data.details);
      return false;
    }
  } catch (error) {
    console.log('❌ Error in new analysis creation:', error.message);
    return false;
  }
}

// Test 5: Verify data persistence
async function testDataPersistence() {
  console.log('\n💾 Test 5: Testing data persistence...');
  
  try {
    const response = await fetch(`http://localhost:4321/api/analysis-progress?analysisId=${testAnalysisId}`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Data persistence working');
      console.log('Content status:', data.contentStatus);
      console.log('Steps completed:', data.completedSteps, '/', data.totalSteps);
      return data.contentStatus?.allContent || false;
    } else {
      console.log('❌ Data persistence issue:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error checking data persistence:', error.message);
    return false;
  }
}

// Run all tests
async function runFixTests() {
  console.log('🧪 Running ConvertIQ Fix Validation Tests...\n');
  
  const test1 = await testDatabaseSchema();
  const test2 = await testAIInsightsGeneration();
  const test3 = await testStepRetry();
  const test4 = await testNewAnalysis();
  
  // Wait for processing
  console.log('\n⏳ Waiting for processing to complete...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  const test5 = await testDataPersistence();
  
  console.log('\n📋 Fix Validation Results:');
  console.log('Database schema:', test1 ? '✅' : '❌');
  console.log('AI insights generation:', test2 ? '✅' : '❌');
  console.log('Step retry mechanism:', test3 ? '✅' : '❌');
  console.log('New analysis creation:', test4 ? '✅' : '❌');
  console.log('Data persistence:', test5 ? '✅' : '❌');
  
  const allPassed = test1 && test2 && test3 && test4 && test5;
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL FIXES WORKING' : '❌ SOME ISSUES REMAIN');
  
  if (allPassed) {
    console.log('\n🎉 ConvertIQ system fixes are successful!');
    console.log(`🔗 Test the UI at: http://localhost:4321/dashboard/analysis/${testAnalysisId}`);
  } else {
    console.log('\n⚠️  Some issues remain. Check the logs above for details.');
  }
}

// Run the tests
runFixTests().catch(console.error);