<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/supabase';
import { Users, MessageCircle, TrendingUp, Target, HelpCircle, Building, AlertCircle, AlertTriangle, Info, Lightbulb } from 'lucide-vue-next';
import MarkdownIt from 'markdown-it';
import DOMPurify from 'dompurify';
import LeadInsightCarousel from './LeadInsightCarousel.vue';

const props = defineProps<{
  analysis: Database['public']['Tables']['analyses']['Row'];
}>();

const md = new MarkdownIt({ html: false, breaks: true, linkify: true });

const targetAudienceHTML = computed(() => {
  const mdSrc = (aiLeadInsights.value as any)?.targetAudienceMarkdown || '';
  try {
    return DOMPurify.sanitize(md.render(mdSrc));
  } catch {
    return '';
  }
});

const aiLeadInsights = ref<{
  businessType: string;
  valueProposition: string;
  targetAudience: string;
  targetAudienceMarkdown?: string;
  valuePropositionDescription?: string;
  businessTypeDescription?: string;
  leadQuestions: Array<{
    questionType: string;
    questionText: string;
    contextExplanation: string;
    qualificationValue: string;
    suggestedResponse: string;
    priorityLevel: number;
  }>;
  businessImpact: string[];
  conversionBarriers: Array<{
    barrier: string;
    evidence: string;
    solution: string;
  }>;
} | null>(null);

const isLoadingAI = ref(false);
const aiError = ref<string | null>(null);
const showTooltip = ref<string | null>(null);

onMounted(async () => {
  await loadAILeadInsights();

  // Listen for refresh events from parent component
  document.addEventListener('refresh-tab-data', (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail?.analysisId === props.analysis.id) {
      console.log('Refreshing lead insights data...');
      loadAILeadInsights();
    }
  });
});

async function loadAILeadInsights() {
  if (!props.analysis) return;

  isLoadingAI.value = true;
  aiError.value = null;

  try {
    // Fetch lead qualification insights
    const { data: existingInsights } = await supabase
      .from('lead_qualification_insights')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .order('priority_level', { ascending: false });

    // Fetch business context data
    const { data: businessContext } = await supabase
      .from('ai_business_context')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .single();

    // Fetch business impact forecast
    const { data: businessImpact } = await supabase
      .from('ai_business_impact_forecast')
      .select('*')
      .eq('analysis_id', props.analysis.id)
      .single();

    // Fetch prospect concerns
    const { data: prospectConcerns } = await supabase
      .from('ai_prospect_concerns')
      .select('*')
      .eq('analysis_id', props.analysis.id);

    if (existingInsights && existingInsights.length > 0) {
      // Use the first insight's stored descriptions as fallback
      const firstInsight = existingInsights[0];

      aiLeadInsights.value = {
        businessType: businessContext?.business_type || firstInsight?.business_type_description || 'Business type analysis in progress',
        valueProposition: businessContext?.value_proposition_primary || firstInsight?.value_proposition_description || 'Value proposition analysis in progress',
        targetAudience: businessContext?.target_audience_primary || firstInsight?.target_audience_markdown || 'Target audience analysis in progress',
        targetAudienceMarkdown: firstInsight?.target_audience_markdown || businessContext?.target_audience_primary || '',
        valuePropositionDescription: firstInsight?.value_proposition_description || businessContext?.value_proposition_primary || '',
        businessTypeDescription: firstInsight?.business_type_description || businessContext?.business_type || '',
        leadQuestions: existingInsights.map(insight => ({
          questionType: insight.question_type,
          questionText: insight.question_text,
          contextExplanation: insight.context_explanation || '',
          qualificationValue: insight.qualification_value || '',
          suggestedResponse: insight.suggested_response || '',
          priorityLevel: insight.priority_level || 3,
          leadScoringWeight: insight.lead_scoring_weight || 1.0
        })),
        businessImpact: businessImpact ? [
          businessImpact.quick_wins_impact_estimate || 'Quick wins identified',
          businessImpact.medium_term_impact_estimate || 'Medium-term opportunities available',
          businessImpact.long_term_impact_estimate || 'Long-term growth potential identified'
        ] : ['Business impact analysis in progress'],
        conversionBarriers: prospectConcerns?.map(concern => ({
          barrier: concern.concern_text,
          evidence: concern.concern_trigger,
          solution: concern.resolution_strategy
        })) || []
      };
    } else {
      // Check if business context exists even without lead insights
      if (businessContext) {
        aiLeadInsights.value = {
          businessType: businessContext.business_type || 'Business type analysis in progress',
          valueProposition: businessContext.value_proposition_primary || 'Value proposition analysis in progress',
          targetAudience: businessContext.target_audience_primary || 'Target audience analysis in progress',
          targetAudienceMarkdown: '',
          valuePropositionDescription: businessContext.value_proposition_primary || '',
          businessTypeDescription: businessContext.business_type || '',
          leadQuestions: [],
          businessImpact: businessImpact ? [
            businessImpact.quick_wins_impact_estimate || 'Quick wins identified',
            businessImpact.medium_term_impact_estimate || 'Medium-term opportunities available',
            businessImpact.long_term_impact_estimate || 'Long-term growth potential identified'
          ] : ['Business impact analysis in progress'],
          conversionBarriers: prospectConcerns?.map(concern => ({
            barrier: concern.concern_text,
            evidence: concern.concern_trigger,
            solution: concern.resolution_strategy
          })) || []
        };
      } else {
        aiError.value = 'Lead insights are being generated. Please wait for the analysis to complete.';
        aiLeadInsights.value = null;
      }
    }
  } catch (error) {
    console.error('Error loading AI lead insights:', error);
    aiError.value = 'Failed to load lead insights. Please refresh the page or try again later.';
    aiLeadInsights.value = null;
  } finally {
    isLoadingAI.value = false;
  }
}

const leadGenerationScore = computed(() => {
  const conversionScore = props.analysis.score || 0;
  const performanceScore = props.analysis.performance_score || 0;
  const seoScore = props.analysis.seo_score || 0;
  const weightedScore = (conversionScore * 0.5) + ((performanceScore / 10) * 0.3) + ((seoScore / 10) * 0.2);
  return Math.round(weightedScore * 10) / 10;
});

const getScoreColor = (score: number) => {
  if (score >= 8) return 'text-green-600';
  if (score >= 6) return 'text-blue-600';
  if (score >= 4) return 'text-yellow-600';
  if (score >= 2) return 'text-orange-600';
  return 'text-red-600';
};

const getScoreBg = (score: number) => {
  if (score >= 8) return 'bg-green-50 border-green-200';
  if (score >= 6) return 'bg-blue-50 border-blue-200';
  if (score >= 4) return 'bg-yellow-50 border-yellow-200';
  if (score >= 2) return 'bg-orange-50 border-orange-200';
  return 'bg-red-50 border-red-200';
};

const getScoreLabel = (score: number) => {
  if (score >= 8) return 'Excellent';
  if (score >= 6) return 'Good';
  if (score >= 4) return 'Fair';
  if (score >= 2) return 'Poor';
  return 'Critical';
};

const toggleTooltip = (tooltipId: string) => {
  showTooltip.value = showTooltip.value === tooltipId ? null : tooltipId;
};

const getTooltipContent = (metric: string) => {
  const tooltips = {
    leadScore: 'Lead Generation Score combines conversion optimization (50%), performance (30%), and SEO (20%) to predict lead capture potential. Scored 0-10.',
    conversionPriority: 'Shows which areas to focus on first for maximum lead generation impact. Based on current scores and improvement potential.',
    targetAudience: 'AI analysis of page content to identify the most likely prospects and their characteristics based on messaging and positioning.'
  };
  return tooltips[metric as keyof typeof tooltips] || '';
};

const getPhaseColor = (phase: string) => {
  if (phase.includes('Immediate')) return 'bg-red-100 text-red-800';
  if (phase.includes('Short-term')) return 'bg-yellow-100 text-yellow-800';
  return 'bg-blue-100 text-blue-800';
};

const roadmapSuggestions = computed(() => {
  if (!aiLeadInsights.value) return [];

  const conversionScore = props.analysis.score || 0;
  const performanceScore = props.analysis.performance_score || 0;
  const seoScore = props.analysis.seo_score || 0;

  const roadmap = [];

  let immediateTitle = 'AI-Identified Quick Wins';
  let immediateDescription = '';
  let immediateImpact = '';

  if (conversionScore < 4) {
    immediateTitle = 'Critical Conversion Fixes';
    immediateDescription = `Based on AI analysis of ${aiLeadInsights.value.businessType.toLowerCase()} content, address fundamental conversion barriers preventing lead capture. Focus on ${aiLeadInsights.value.valueProposition} clarity and removing friction from key user flows.`;
    immediateImpact = '25-40% increase in lead capture rate';
  } else if (conversionScore < 6) {
    immediateTitle = 'Conversion Optimization';
    immediateDescription = `AI analysis reveals opportunities to enhance ${aiLeadInsights.value.businessType.toLowerCase()} conversion tactics. Implement proven strategies for ${aiLeadInsights.value.targetAudience.toLowerCase()} audience.`;
    immediateImpact = '15-25% increase in lead capture rate';
  } else {
    immediateTitle = 'Conversion Fine-tuning';
    immediateDescription = `AI analysis shows strong foundation. Optimize existing elements with A/B testing and minor improvements targeting ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    immediateImpact = '10-15% increase in lead capture rate';
  }

  roadmap.push({
    phase: 'Immediate (0-2 weeks)',
    title: immediateTitle,
    description: immediateDescription,
    timeline: '1-2 weeks',
    expectedImpact: immediateImpact
  });

  let shortTermTitle = 'AI-Guided Trust & Performance';
  let shortTermDescription = '';
  let shortTermImpact = '';

  if (performanceScore < 60) {
    shortTermTitle = 'Performance & Trust Building';
    shortTermDescription = `AI analysis indicates performance issues are hurting credibility for ${aiLeadInsights.value.businessType.toLowerCase()}. Improve page loading speeds and add trust signals specific to ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    shortTermImpact = '20-35% improvement in lead quality and conversion';
  } else if (seoScore < 70) {
    shortTermTitle = 'SEO & Visibility Enhancement';
    shortTermDescription = `AI analysis reveals SEO opportunities for ${aiLeadInsights.value.businessType.toLowerCase()}. Optimize for search engines to increase organic traffic and add credibility elements.`;
    shortTermImpact = '15-25% improvement in lead volume and quality';
  } else {
    shortTermTitle = 'Advanced Trust Building';
    shortTermDescription = `AI analysis shows strong foundation. Implement sophisticated trust elements targeting ${aiLeadInsights.value.targetAudience.toLowerCase()} to qualify and convert high-value prospects.`;
    shortTermImpact = '15-25% improvement in lead quality';
  }

  roadmap.push({
    phase: 'Short-term (1-3 months)',
    title: shortTermTitle,
    description: shortTermDescription,
    timeline: '4-8 weeks',
    expectedImpact: shortTermImpact
  });

  const overallScore = Math.round((conversionScore + (performanceScore / 10) + (seoScore / 10)) / 3);
  let longTermTitle = 'AI-Optimized Lead System';
  let longTermDescription = '';
  let longTermImpact = '';

  if (overallScore < 5) {
    longTermTitle = 'Complete Lead System Overhaul';
    longTermDescription = `AI analysis recommends comprehensive lead generation system for ${aiLeadInsights.value.businessType.toLowerCase()} with progressive profiling, automated nurturing, and advanced analytics.`;
    longTermImpact = '40-60% increase in qualified leads and revenue';
  } else if (overallScore < 7) {
    longTermTitle = 'Lead Intelligence & Automation';
    longTermDescription = `AI insights suggest deploying lead scoring, behavioral tracking, and automated qualification processes optimized for ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    longTermImpact = '25-40% increase in qualified leads';
  } else {
    longTermTitle = 'Premium Lead Experience';
    longTermDescription = `AI analysis indicates readiness for personalized lead journeys with dynamic content, AI-powered recommendations, and premium touchpoints for ${aiLeadInsights.value.targetAudience.toLowerCase()}.`;
    longTermImpact = '20-30% increase in high-value qualified leads';
  }

  roadmap.push({
    phase: 'Long-term (3-6 months)',
    title: longTermTitle,
    description: longTermDescription,
    timeline: '3-6 months',
    expectedImpact: longTermImpact
  });

  return roadmap;
});

const businessImpact = computed(() => {
  if (!aiLeadInsights.value) return [];
  
  return aiLeadInsights.value.businessImpact.length > 0 
    ? aiLeadInsights.value.businessImpact 
    : [
        `AI analysis reveals optimization potential for ${aiLeadInsights.value.businessType.toLowerCase()}`,
        `Target audience insights show opportunities with ${aiLeadInsights.value.targetAudience.toLowerCase()}`,
        'Implementing AI recommendations could increase lead quality by 25-40%',
        'Performance improvements directly correlate with lead conversion rates'
      ];
});

const getImpactCardStyle = (index: number) => {
  const styles = [
    'bg-blue-50 border-blue-200',
    'bg-green-50 border-green-200',
    'bg-purple-50 border-purple-200',
    'bg-orange-50 border-orange-200'
  ];
  return styles[index % styles.length];
};

const getImpactIconStyle = (index: number) => {
  const styles = [
    'bg-blue-100 text-blue-600',
    'bg-green-100 text-green-600',
    'bg-purple-100 text-purple-600',
    'bg-orange-100 text-orange-600'
  ];
  return styles[index % styles.length];
};

const getImpactIcon = (index: number) => {
  const icons = [TrendingUp, Target, Users, Lightbulb];
  return icons[index % icons.length];
};

const getImpactTitle = (index: number) => {
  const titles = [
    'Revenue Impact',
    'Lead Quality',
    'Market Opportunity',
    'Strategic Advantage'
  ];
  return titles[index % titles.length];
};
</script>

<template>
  <div class="space-y-8">
    <!-- AI Lead Generation Overview -->
    <div class="bg-white">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <Users class="w-5 h-5 text-gray-400 mr-2" />
          <h3 class="text-lg font-semibold text-gray-900">AI-Powered Lead Generation Analysis</h3>
        </div>
        
        <div class="flex items-center space-x-3">
          <div class="text-right">
            <p class="text-sm text-gray-500">Lead Gen Score</p>
          </div>
          <div 
            class="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold"
            :class="getScoreBg(leadGenerationScore) + ' ' + getScoreColor(leadGenerationScore)"
          >
            {{ Math.round(leadGenerationScore) }}
          </div>
        </div>
      </div>

      <div v-if="aiLeadInsights" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="rounded-lg p-4 border bg-indigo-50 border-indigo-200">
            <h4 class="font-medium text-indigo-900 mb-2 flex items-center">
              <Building class="w-4 h-4 mr-2" />
              Business Type
            </h4>
            <p class="text-sm text-indigo-800 font-semibold">{{ aiLeadInsights.businessType }}</p>
            <p v-if="(aiLeadInsights as any).businessTypeDescription" class="text-sm text-indigo-800 mt-1">
              {{ (aiLeadInsights as any).businessTypeDescription }}
            </p>
          </div>

          <div class="rounded-lg p-4 border bg-yellow-50 border-yellow-200">
            <h4 class="font-medium text-yellow-900 mb-2 flex items-center">
              <Target class="w-4 h-4 mr-2" />
              Value Proposition
            </h4>
            <p class="text-sm text-yellow-900 font-semibold">{{ aiLeadInsights.valueProposition }}</p>
            <p v-if="(aiLeadInsights as any).valuePropositionDescription" class="text-sm text-yellow-800 mt-1">
              {{ (aiLeadInsights as any).valuePropositionDescription }}
            </p>
          </div>
        </div>
        <div class="grid grid-cols-1 gap-4">
          <div class="rounded-lg p-4 border bg-green-50 border-green-200">
            <h4 class="font-medium text-green-900 mb-2 flex items-center">
              <Users class="w-4 h-4 mr-2" />
              Target Audience
            </h4>
            <p class="text-sm text-green-800 font-semibold mb-2">{{ aiLeadInsights.targetAudience }}</p>
            <div v-if="targetAudienceHTML" class="prose prose-sm max-w-none text-green-900" v-html="targetAudienceHTML"></div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-4">
            <MessageCircle class="w-5 h-5 text-gray-400 mr-2" />
            <h4 class="text-lg font-semibold text-gray-900">AI-Generated Lead Qualification Questions</h4>
          </div>
          <p class="text-gray-600 mb-6">Based on AI analysis of your page content, potential leads might ask these specific questions:</p>
          <LeadInsightCarousel :questions="aiLeadInsights.leadQuestions" />
        </div>

        <div v-if="aiLeadInsights.conversionBarriers.length > 0" class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-4">
            <AlertTriangle class="w-5 h-5 text-gray-400 mr-2" />
            <h4 class="text-lg font-semibold text-gray-900">AI-Identified Conversion Barriers</h4>
          </div>
          
          <div class="space-y-4">
            <div 
              v-for="(barrier, index) in aiLeadInsights.conversionBarriers" 
              :key="index"
              class="border border-red-200 rounded-lg p-4 bg-red-50"
            >
              <h5 class="font-medium text-red-900 mb-2">{{ barrier.barrier }}</h5>
              <div class="space-y-2 text-sm">
                <div>
                  <p class="font-medium text-red-800">Evidence found:</p>
                  <p class="text-red-700">{{ barrier.evidence }}</p>
                </div>
                <div class="bg-red-100 rounded p-2 border border-red-300">
                  <p class="font-medium text-red-900 mb-1">
                    <Lightbulb class="w-3 h-3 inline mr-1" />
                    AI Recommendation:
                  </p>
                  <p class="text-red-800">{{ barrier.solution }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-6">
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            <h3 class="text-lg font-semibold text-gray-900">AI-Generated Lead Generation Roadmap</h3>
          </div>
          
          <div class="space-y-6">
            <div 
              v-for="(suggestion, index) in roadmapSuggestions" 
              :key="index"
              class="border border-gray-200 rounded-lg p-6"
            >
              <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                  <span 
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                    :class="getPhaseColor(suggestion.phase)"
                  >
                    {{ suggestion.phase }}
                  </span>
                </div>
                <div class="text-right text-sm text-gray-500">
                  {{ suggestion.timeline }}
                </div>
              </div>
              
              <h4 class="text-lg font-medium text-gray-900 mb-2">{{ suggestion.title }}</h4>
              <p class="text-gray-700 mb-4">{{ suggestion.description }}</p>
              
              <div class="bg-green-50 rounded-lg p-3 border border-green-200">
                <div class="flex items-center">
                  <TrendingUp class="w-4 h-4 text-green-600 mr-2" />
                  <span class="text-sm font-medium text-green-900">Expected Impact:</span>
                  <span class="text-sm text-green-800 ml-2">{{ suggestion.expectedImpact }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <div class="flex items-center mb-6">
            <TrendingUp class="w-5 h-5 text-gray-400 mr-2" />
            <h3 class="text-lg font-semibold text-gray-900">AI Business Impact Analysis</h3>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="(insight, index) in businessImpact"
              :key="index"
              class="p-4 rounded-lg border transition-all duration-200 hover:shadow-md"
              :class="getImpactCardStyle(index)"
            >
              <div class="flex items-start">
                <div class="flex-shrink-0 mr-3">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center"
                       :class="getImpactIconStyle(index)">
                    <component :is="getImpactIcon(index)" class="w-4 h-4" />
                  </div>
                </div>
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900 mb-2">{{ getImpactTitle(index) }}</h4>
                  <p class="text-sm text-gray-700 leading-relaxed">{{ insight }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="aiError" class="text-center py-12">
        <AlertTriangle class="w-8 h-8 text-yellow-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">AI Analysis Unavailable</h3>
        <p class="text-gray-600 mb-4">{{ aiError }}</p>
        <button 
          @click="loadAILeadInsights" 
          class="btn-primary"
        >
          Retry AI Analysis
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
